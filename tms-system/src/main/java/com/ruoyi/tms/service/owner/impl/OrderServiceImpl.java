package com.ruoyi.tms.service.owner.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.security.Md5Utils;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.tms.constant.SegmentStatusEnum;
import com.ruoyi.tms.constant.carrier.EntrustLotStatusEnum;
import com.ruoyi.tms.constant.carrier.EntrustStatusEnum;
import com.ruoyi.tms.constant.invoice.InvoiceSegmentEnum;
import com.ruoyi.tms.constant.invoice.InvoiceSrcEnum;
import com.ruoyi.tms.constant.InvoiceStatusEnum;
import com.ruoyi.tms.constant.mobile.MobileUserType;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.CarrierUser;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.invoice.InvPackGoods;
import com.ruoyi.tms.domain.invoice.MultipleShippingAddress;
import com.ruoyi.tms.domain.invoice.MultipleShippingGoods;
import com.ruoyi.tms.domain.owner.CustEvaluate;
import com.ruoyi.tms.domain.owner.CustEvaluateDtl;
import com.ruoyi.tms.domain.owner.Order;
import com.ruoyi.tms.domain.owner.OrderGoods;
import com.ruoyi.tms.domain.segment.SegPackGoods;
import com.ruoyi.tms.domain.segment.Segment;
import com.ruoyi.tms.domain.trace.CarLocus;
import com.ruoyi.tms.domain.trace.EntPackGoods;
import com.ruoyi.tms.mapper.basic.CarrierMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.invoice.MultipleShippingAddressMapper;
import com.ruoyi.tms.mapper.invoice.MultipleShippingGoodsMapper;
import com.ruoyi.tms.mapper.owner.OrderMapper;
import com.ruoyi.tms.mapper.segment.SegmentMapper;
import com.ruoyi.tms.mapper.trace.CarLocusMapper;
import com.ruoyi.tms.mapper.trace.EntPackGoodsMapper;
import com.ruoyi.tms.service.carrier.IEntrustLotService;
import com.ruoyi.tms.service.carrier.IEntrustService;
import com.ruoyi.tms.service.mobile.MobileUserService;
import com.ruoyi.tms.service.owner.IOrderService;
import com.ruoyi.tms.service.segment.ISegPackGoodsService;
import com.ruoyi.tms.service.trace.ITraceService;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.util.GetTransLineUtil;
import com.ruoyi.tms.vo.business.EvaluateTotalVO;
import com.ruoyi.tms.vo.client.ClientVO;
import com.ruoyi.tms.vo.owner.AddressVO;
import com.ruoyi.util.ShiroUtils;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发货单 服务层实现
 *
 * <AUTHOR>
 * @date 2019-09-17
 */
@Service
public class OrderServiceImpl implements IOrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private EntrustMapper entrustMapper;
    @Autowired
    private CarLocusMapper carLocusMapper;
    @Autowired
    private ITraceService traceService;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private MultipleShippingAddressMapper multipleShippingAddressMapper;
    @Autowired
    private MultipleShippingGoodsMapper multipleShippingGoodsMapper;
    @Autowired
    private CarrierMapper carrierMapper;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private MobileUserService mobileUserService;
    @Autowired
    private SegmentMapper segmentMapper;
    @Autowired
    private ISegPackGoodsService segPackGoodsService;
    @Autowired
    private IEntrustService entrustService;
    @Autowired
    private IEntrustLotService entrustLotService;
    @Autowired
    private EntPackGoodsMapper entPackGoodsMapper;

    /**
     * 查询发货单信息
     *
     * @param orderId 发货单ID
     * @return 发货单信息
     */
    @Override
    public Map<String, Object> selectOrderById(String orderId, int flag) {
        Map<String, Object> map = new HashMap<>();
        Order order = orderMapper.selectOrderById(orderId);
        if (flag == 1) {
            order.setInvoiceId("");
        }
        List<OrderGoods> goodsList = orderMapper.selectOrderGoods(orderId);
        map.put("order", order);
        //货物集合
        map.put("goodsList", goodsList);
        map.put("goodsListSize", goodsList.size());
        return map;
    }

    /**
     * 查询发货单列表
     *
     * @param order 发货单信息
     * @return 发货单集合
     */
    @Override
    public List<Order> selectOrderList(Order order) {
        return orderMapper.selectOrderList(order);
    }

    /**
     * 查询发货单列表
     *
     * @param client
     * @return 发货单集合
     */
    @Override
    public List<OrderGoods> selectOrderGoodsList(ClientVO client) {
        return orderMapper.selectOrderGoodsList(client);
    }

    /**
     * 新增发货单
     *
     * @param order 发货单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertOrder(Order order) {
        String deliContact = order.getDeliContact().trim();
        if (deliContact.length() < 2) {
            return AjaxResult.error("发货方联系人名称必须大于一个字符！");
        }
        String arriContact = order.getArriContact().trim();
        if (arriContact.length() < 2) {
            return AjaxResult.error("收货方联系人名称必须大于一个字符！");
        }


        //设置发货单id uuid
        String invoiceId = IdUtil.simpleUUID();
        //结算公司
        String balaCorpId = order.getBalaCorpId();
        //设置发货单号 逻辑：结算公司编号 + 当前年月日 + 四位Sequence。例如：MY201909150001
        String seqInvoice = StrUtil.fillBefore(orderMapper.getSeqInvoice() + "", '0', 4);
        String vbillno = balaCorpId + DateUtil.format(new Date(), "yyyyMMdd") + seqInvoice;
        //
        List<OrderGoods> goodsList = order.getGoodsList().stream().filter(x -> StringUtils.isNotEmpty(x.getGoodsName())).collect(Collectors.toList());

        //总体积
        double volumeCount = goodsList.stream().mapToDouble(x -> x.getVolume() == null ? 0 : x.getVolume()).sum();
        //总件数
        double numCount = goodsList.stream().mapToDouble(x -> x.getNum() == null ? 0 : x.getNum()).sum();
        //总重量
        double weightCount = goodsList.stream().mapToDouble(x -> x.getWeight() == null ? 0 : x.getWeight()).sum();
        //货品名称
        String goodsName = goodsList.stream().map(OrderGoods::getGoodsName).distinct().collect(Collectors.joining(","));
        //客户发货单单
        String custOrderno = goodsList.stream().map(OrderGoods::getCustOrderno).distinct().collect(Collectors.joining(","));

        MultipleShippingAddress deli = new MultipleShippingAddress();
        String deliId = IdUtil.simpleUUID();

        deli.setMultipleShippingAddressId(deliId);
        //地址ID
        deli.setDeliveryId(order.getDeliveryId());
        //省id
        deli.setProvinceId(order.getDeliProvinceId());
        //市id
        deli.setCityId(order.getDeliCityId());
        //区id
        deli.setAreaId(order.getDeliAreaId());
        //省名称
        deli.setProvinceName(order.getDeliProName());
        //市名称
        deli.setCityName(order.getDeliCityName());
        //区名称
        deli.setAreaName(order.getDeliAreaName());
        //地址code
        deli.setAddrCode(order.getDeliAddrCode());
        //地址名称
        deli.setAddrName(order.getDeliAddrName());
        //详细地址
        deli.setDetailAddr(order.getDeliDetailAddr());
        //提货联系人
        deli.setContact(order.getDeliContact());
        //提货联系人
        deli.setMobile(order.getDeliMobile());
        //地址类型  0发货地址  1收货地址
        deli.setAddressType(0);
        deli.setInvoiceId(invoiceId);
        deli.setInvoiceNo(vbillno);

        deli.setVolumeCount(volumeCount);
        deli.setNumCount(numCount);
        deli.setWeightCount(weightCount);
        deli.setGoodsName(goodsName);

        for (OrderGoods orderGoods : goodsList) {
            MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
            BeanUtils.copyBeanProp(multipleShippingGoods, orderGoods);
            multipleShippingGoods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
            multipleShippingGoods.setMultipleShippingAddressId(deliId);
            multipleShippingGoods.setRegScrId("OrderServiceImpl.insertOrder");
            multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
        }

        multipleShippingAddressMapper.insertSelective(deli);

        MultipleShippingAddress arri = new MultipleShippingAddress();
        String arriId = IdUtil.simpleUUID();

        arri.setMultipleShippingAddressId(arriId);
        //地址ID
        arri.setDeliveryId(order.getDeliveryId());
        //省id
        arri.setProvinceId(order.getDeliProvinceId());
        //市id
        arri.setCityId(order.getDeliCityId());
        //区id
        arri.setAreaId(order.getDeliAreaId());
        //省名称
        arri.setProvinceName(order.getDeliProName());
        //市名称
        arri.setCityName(order.getDeliCityName());
        //区名称
        arri.setAreaName(order.getDeliAreaName());
        //地址code
        arri.setAddrCode(order.getDeliAddrCode());
        //地址名称
        arri.setAddrName(order.getDeliAddrName());
        //详细地址
        arri.setDetailAddr(order.getDeliDetailAddr());
        //提货联系人
        arri.setContact(order.getDeliContact());
        //提货联系人
        arri.setMobile(order.getDeliMobile());
        //地址类型  0发货地址  1收货地址
        arri.setAddressType(0);
        arri.setInvoiceId(invoiceId);
        arri.setInvoiceNo(vbillno);

        arri.setVolumeCount(volumeCount);
        arri.setNumCount(numCount);
        arri.setWeightCount(weightCount);
        arri.setGoodsName(goodsName);

        for (OrderGoods orderGoods : goodsList) {
            MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
            BeanUtils.copyBeanProp(multipleShippingGoods, orderGoods);
            multipleShippingGoods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
            multipleShippingGoods.setMultipleShippingAddressId(arriId);
            multipleShippingGoods.setRegScrId("OrderServiceImpl.insertOrder");
            multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
        }
        multipleShippingAddressMapper.insertSelective(arri);

        /*
         * 添加发货单主表信息
         */
        order.setInvoiceId(invoiceId);
        //发货单新建
        order.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
        //删除标记 0 未删除
        order.setDelFlag(0);


        order.setVbillno(vbillno);

        //不为多装多卸
        order.setIsMultiple(0);
        order.setIsFleetData("0");
        //是否存在对应的业务或者车队数据  0不存在
        order.setIsFleetAssign("0");
        //设置调度状态
        order.setSegmentStatus(InvoiceSegmentEnum.NO_DISPATCH.getValue());
        //是否加入对账单 0未加入 1部分加入 2全部加入
        order.setIsAddReceCheck(0);
        //应收核销状态 0未核销  1部分核销 2已核销
        order.setReceivableWriteOffStatus("0");

        order.setRegScrId("order");
        order.setCorScrId("order");
        order.setSrcType(InvoiceSrcEnum.CONSIGNOR.getValue());

        order.setGoodsName(goodsName);
        order.setCustOrderno(custOrderno);

        //新增发货单
        int row = orderMapper.insertOrder(order);

        /*
         * 添加发货单货品明细
         */
        for (OrderGoods goods : goodsList) {
            if (goods.getGoodsName() != null) {
                //发货单货品id
                goods.setInvPackGoodsId(IdUtil.simpleUUID());
                //发货单货品 删除标记 0 未删除
                goods.setDelFlag(0);
                //发货单 id
                goods.setInvoiceId(invoiceId);
                goods.setRegScrId("OrderServiceImpl.insertOrder");
                goods.setCorScrId("OrderServiceImpl.insertOrder");
                orderMapper.insertOrderGoods(goods);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 修改发货单
     *
     * @param order 发货单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOrder(Order order) {
        List<OrderGoods> goodsList = order.getGoodsList().stream().filter(x -> StringUtils.isNotEmpty(x.getGoodsName())).collect(Collectors.toList());

        //设置发货单id uuid
        String invoiceId = IdUtil.simpleUUID();

        /*
         * 删除老的发货单
         */
        Order orderDel = new Order();
        orderDel.setInvoiceId(order.getInvoiceId());
        orderDel.setDelFlag(1);
        orderDel.setDelDate(new Date());
        orderDel.setDelUserid(shiroUtils.getUserId().toString());
        orderDel.setCorScrId("OrderServiceImpl.updateOrder");
        orderMapper.updateOrder(orderDel);

        /*
         * 删除老的货品
         */
        orderMapper.deleteOrderGoodsById(order.getInvoiceId());

        /*
         * 删除 t_multiple_shipping_address和t_multiple_shipping_goods
         */
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper
                .selectListByInvoiceId(order.getInvoiceId());
        for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
            //删除地址
            MultipleShippingAddress delAddr = new MultipleShippingAddress();
            delAddr.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
            delAddr.setDelFlag(1);
            delAddr.setDelDate(new Date());
            delAddr.setCorScrId("OrderServiceImpl.updateOrder");
            multipleShippingAddressMapper.updateByPrimaryKeySelective(delAddr);

            //删除地址对应的货品
            MultipleShippingGoods delGoods = new MultipleShippingGoods();
            delGoods.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
            delGoods.setDelFlag(1);
            delGoods.setDelDate(new Date());
            delGoods.setCorScrId("OrderServiceImpl.updateOrder");
            multipleShippingGoodsMapper.updateByAddressIdSelective(delGoods);
        }


        //总体积
        double volumeCount = goodsList.stream().mapToDouble(x -> x.getVolume() == null ? 0 : x.getVolume()).sum();
        //总件数
        double numCount = goodsList.stream().mapToDouble(x -> x.getNum() == null ? 0 : x.getNum()).sum();
        //总重量
        double weightCount = goodsList.stream().mapToDouble(x -> x.getWeight() == null ? 0 : x.getWeight()).sum();
        //货品名称
        String goodsName = goodsList.stream()
                .map(OrderGoods::getGoodsName)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.joining(","));
        //客户发货单单
        String custOrderno = goodsList.stream()
                .map(OrderGoods::getCustOrderno)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.joining(","));

        /*
         * 插入最新的发货单
         */
        order.setInvoiceId(invoiceId);
        //发货单新建
        order.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
        //删除标记 0 未删除
        order.setDelFlag(0);
        //不为多装多卸
        order.setIsMultiple(0);
        order.setIsFleetData("0");
        //是否存在对应的业务或者车队数据  0不存在
        order.setIsFleetAssign("0");
        //设置调度状态
        order.setSegmentStatus(InvoiceSegmentEnum.NO_DISPATCH.getValue());
        //是否加入对账单 0未加入 1部分加入 2全部加入
        order.setIsAddReceCheck(0);
        //应收核销状态 0未核销  1部分核销 2已核销
        order.setReceivableWriteOffStatus("0");

        order.setRegScrId("OrderServiceImpl.updateOrder");
        order.setCorScrId("OrderServiceImpl.updateOrder");
        order.setSrcType(InvoiceSrcEnum.CONSIGNOR.getValue());

        order.setGoodsName(goodsName);
        order.setCustOrderno(custOrderno);
        //新增发货单
        int row = orderMapper.insertOrder(order);

        /*
         * 插入地址表
         */
        MultipleShippingAddress deli = new MultipleShippingAddress();
        String deliId = IdUtil.simpleUUID();

        deli.setMultipleShippingAddressId(deliId);
        //地址ID
        deli.setDeliveryId(order.getDeliveryId());
        //省id
        deli.setProvinceId(order.getDeliProvinceId());
        //市id
        deli.setCityId(order.getDeliCityId());
        //区id
        deli.setAreaId(order.getDeliAreaId());
        //省名称
        deli.setProvinceName(order.getDeliProName());
        //市名称
        deli.setCityName(order.getDeliCityName());
        //区名称
        deli.setAreaName(order.getDeliAreaName());
        //地址code
        deli.setAddrCode(order.getDeliAddrCode());
        //地址名称
        deli.setAddrName(order.getDeliAddrName());
        //详细地址
        deli.setDetailAddr(order.getDeliDetailAddr());
        //提货联系人
        deli.setContact(order.getDeliContact());
        //提货联系人
        deli.setMobile(order.getDeliMobile());
        //地址类型  0发货地址  1收货地址
        deli.setAddressType(0);
        deli.setInvoiceId(order.getInvoiceId());
        deli.setInvoiceNo(order.getVbillno());

        deli.setVolumeCount(volumeCount);
        deli.setNumCount(numCount);
        deli.setWeightCount(weightCount);
        deli.setGoodsName(goodsName);

        for (OrderGoods orderGoods : goodsList) {
            MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
            BeanUtils.copyBeanProp(multipleShippingGoods, orderGoods);
            multipleShippingGoods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
            multipleShippingGoods.setMultipleShippingAddressId(deliId);
            multipleShippingGoods.setRegScrId("OrderServiceImpl.updateOrder");
            multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
        }
        multipleShippingAddressMapper.insertSelective(deli);

        MultipleShippingAddress arri = new MultipleShippingAddress();
        String arriId = IdUtil.simpleUUID();

        arri.setMultipleShippingAddressId(arriId);
        //地址ID
        arri.setDeliveryId(order.getDeliveryId());
        //省id
        arri.setProvinceId(order.getDeliProvinceId());
        //市id
        arri.setCityId(order.getDeliCityId());
        //区id
        arri.setAreaId(order.getDeliAreaId());
        //省名称
        arri.setProvinceName(order.getDeliProName());
        //市名称
        arri.setCityName(order.getDeliCityName());
        //区名称
        arri.setAreaName(order.getDeliAreaName());
        //地址code
        arri.setAddrCode(order.getDeliAddrCode());
        //地址名称
        arri.setAddrName(order.getDeliAddrName());
        //详细地址
        arri.setDetailAddr(order.getDeliDetailAddr());
        //提货联系人
        arri.setContact(order.getDeliContact());
        //提货联系人
        arri.setMobile(order.getDeliMobile());
        //地址类型  0发货地址  1收货地址
        arri.setAddressType(0);
        arri.setInvoiceId(order.getInvoiceId());
        arri.setInvoiceNo(order.getVbillno());

        arri.setVolumeCount(volumeCount);
        arri.setNumCount(numCount);
        arri.setWeightCount(weightCount);
        arri.setGoodsName(goodsName);

        for (OrderGoods orderGoods : goodsList) {
            MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
            BeanUtils.copyBeanProp(multipleShippingGoods, orderGoods);
            multipleShippingGoods.setMultipleShippingGoodsId(IdUtil.simpleUUID());
            multipleShippingGoods.setMultipleShippingAddressId(arriId);
            multipleShippingGoods.setRegScrId("OrderServiceImpl.updateOrder");
            multipleShippingGoodsMapper.insertSelective(multipleShippingGoods);
        }
        multipleShippingAddressMapper.insertSelective(arri);

        /*
         * 添加发货单货品明细
         */
        for (OrderGoods goods : goodsList) {
            if (goods.getGoodsName() != null) {
                //发货单货品id
                goods.setInvPackGoodsId(IdUtil.simpleUUID());
                //发货单货品 删除标记 0 未删除
                goods.setDelFlag(0);
                //发货单 id
                goods.setInvoiceId(order.getInvoiceId());
                goods.setRegScrId("OrderServiceImpl.updateOrder");
                goods.setCorScrId("OrderServiceImpl.updateOrder");
                orderMapper.insertOrderGoods(goods);
            }
        }
        return row;
    }

    /**
     * 删除发货单对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteOrderByIds(String ids) {
        String[] idList = Convert.toStrArray(ids);
        //查询 需删除 的发货单，是否存在非新建状态下的。如果存在则无法删除
        List<Order> orderList = orderMapper.getNoNewListByIds(idList, InvoiceStatusEnum.NEW.getValue());
        if (orderList.size() > 0) {
            return AjaxResult.error("存在非新建状态下的发货单！无法删除！");
        }
        /*
         * 删除 发货单下 所有 货品明细信息，逻辑删除
         */
        OrderGoods orderGoods = new OrderGoods();
        orderGoods.setDelFlag(1);
        orderGoods.setDelDate(new Date());
        for (String id : idList) {
            orderGoods.setInvoiceId(id);
            orderMapper.updateOrderGoodsByOrderId(orderGoods);
        }

        /*
         * 删除 t_multiple_shipping_address和t_multiple_shipping_goods
         */
        for (String id : idList) {
            List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressMapper
                    .selectListByInvoiceId(id);
            for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
                //删除地址
                MultipleShippingAddress delAddr = new MultipleShippingAddress();
                delAddr.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                delAddr.setDelFlag(1);
                delAddr.setDelDate(new Date());
                delAddr.setCorScrId("OrderServiceImpl.updateOrder");
                multipleShippingAddressMapper.updateByPrimaryKeySelective(delAddr);

                //删除地址对应的货品
                MultipleShippingGoods delGoods = new MultipleShippingGoods();
                delGoods.setMultipleShippingAddressId(multipleShippingAddress.getMultipleShippingAddressId());
                delGoods.setDelFlag(1);
                delGoods.setDelDate(new Date());
                delGoods.setCorScrId("OrderServiceImpl.updateOrder");
                multipleShippingGoodsMapper.updateByAddressIdSelective(delGoods);
            }
        }

        //删除发货单 逻辑删除
        int i = orderMapper.deleteOrdersByIdsLogic(idList, shiroUtils.getUserId().toString(), InvoiceStatusEnum.NEW.getValue());
        if (i != idList.length) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("删除失败，刷新列表后重试。");
        }

        return AjaxResult.success();
    }

    /**
     * 查询客户信息
     *
     * @param userId 登录Id
     * @return
     */
    @Override
    public ClientVO selectCustNameById(long userId) {
        return orderMapper.selectCustById(userId);
    }

    /**
     * 查询发货单异常
     *
     * @param orderId 发货单id
     * @return
     */
    @Override
    public List<Map<String, Object>> selectExceptionByOrderId(String orderId) {
        return orderMapper.selectExceptionByOrderId(orderId);
    }

    /**
     * 查询异常图片
     *
     * @param expId 异常id
     * @return
     */
    @Override
    public List<Map<String, Object>> selectExpPicByExpId(String expId) {
        return orderMapper.selectExpPicByExpId(expId);
    }

    /**
     * 查询发货单单个异常详情
     *
     * @param expId 异常id
     * @param
     * @return
     */
    @Override
    public Map<String, Object> selectExpByExpId(String expId) {
        return orderMapper.selectExpByExpId(expId);
    }

    /**
     * 新增和更新评价
     *
     * @param params 评价
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveRating(Map<String, String> params) throws Exception {
        //发货单id
        String orderId = params.get("orderId");
        //发货单号
        String orderNo = params.get("orderNo");
        //发货单信息
        Order orderInfo = orderMapper.selectOrderById(orderId);
        //发货单状态 5,6,7 才能进行评价
        if (!(InvoiceStatusEnum.ARRIVALS.getValue().equals(orderInfo.getVbillstatus()) ||
                InvoiceStatusEnum.PORTION_RETURNS.getValue().equals(orderInfo.getVbillstatus()) ||
                InvoiceStatusEnum.RETURNS.getValue().equals(orderInfo.getVbillstatus()))) {
            throw new BusinessException("评价失败，请手动刷新");
        }
        //发货单信息
        Order order = new Order();
        order.setInvoiceId(orderId);
        order.setCorDate(new Date());
        order.setCorUserId(shiroUtils.getUserId().toString());
        order.setCorUserName(shiroUtils.getSysUser().getUserName());
        order.setCorScrId("order");
        //更新评价状态乐观锁
        int rows1 = orderMapper.updateOrderIsRating(order);
        if (rows1 == 0) {
            throw new BusinessException("评价失败，请手动刷新");
        }
        //新增评价主表
        CustEvaluate custEvaluate = new CustEvaluate();
        String evaluateId = IdUtil.simpleUUID();
        custEvaluate.setCustEvaluateId(evaluateId);
        custEvaluate.setInvoiceId(orderId);
        custEvaluate.setInvoiceNo(orderNo);
        custEvaluate.setMemo(params.get("memo"));
        custEvaluate.setRegScrId("order");
        custEvaluate.setCorScrId("order");
        int rows2 = orderMapper.insertCustEvaluate(custEvaluate);
        if (rows2 == 0) {
            throw new BusinessException("评价新增失败");
        }

        //新增评价明细表
        List<Map<String, Object>> list = orderMapper.selectDictInfo();
        int rows3 = 0;
        for (int i = 0; i < list.size(); i++) {
            String dicValue = list.get(i).get("DICT_VALUE").toString();
            int ratingStar = Integer.valueOf(params.get("rating_" + dicValue));
            CustEvaluateDtl custEvaluateDtl = new CustEvaluateDtl();
            custEvaluateDtl.setCustEvaluateDtlId(IdUtil.simpleUUID());
            custEvaluateDtl.setCustEvaluateId(evaluateId);
            custEvaluateDtl.setDispatcherGrade(ratingStar);
            custEvaluateDtl.setEvaluateType(dicValue);
            custEvaluateDtl.setRegScrId("order");
            custEvaluateDtl.setCorScrId("order");
            rows3 = orderMapper.insertCustEvaluateDtl(custEvaluateDtl);
            if (rows3 == 0) {
                throw new BusinessException("评价新增失败");
            }
        }

        return AjaxResult.success();
    }

    /**
     * 根据发货单ID查询已有的评价信息,原来没有评价，星级补5
     *
     * @param orderId 发货单ID
     * @return
     */
    @Override
    public Map<String, Object> selectRatingInfoByOrderId(String orderId) {
        //封装Map
        Map<String, Object> map = new HashMap<>();
        //根据发货单id 查询发货单信息
        Order order = orderMapper.selectOrderById(orderId);
        //根据发货单id 查询评价主表
        CustEvaluate evaluate = orderMapper.selectEvaluate(orderId);
        //封装评价明细list
        List<CustEvaluateDtl> list = new ArrayList<>();
        //查询字典表信息
        List<Map<String, Object>> dictInfo = orderMapper.selectDictInfo();
        //判断是否评价
        if (StringUtils.isNull(evaluate)) {
            //默认星级补5
            CustEvaluate custEvaluate = new CustEvaluate();
            custEvaluate.setMemo("");
            map.put("evaluate", custEvaluate);
            for (int i = 0; i < dictInfo.size(); i++) {
                String dicValue = dictInfo.get(i).get("DICT_VALUE").toString();
                CustEvaluateDtl custEvaluateDtl = new CustEvaluateDtl();
                custEvaluateDtl.setEvaluateType(dicValue);
                custEvaluateDtl.setDispatcherGrade(5);
                list.add(custEvaluateDtl);
            }
        } else {
            map.put("evaluate", evaluate);
            //查询评价详情
            list = orderMapper.selectRatingInfoById(evaluate.getCustEvaluateId());
        }
        map.put("list", list);
        map.put("orderNo", order.getVbillno());
        return map;
    }

    /**
     * 物流跟踪
     *
     * @param orderId
     * @return
     */
    @Override
    public Map<String, Object> selectLocation(String orderId) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> pointMap = new HashMap<>();

        Order order = orderMapper.selectOrderById(orderId);
        pointMap.put("deliAddr", order.getDeliProName() + order.getDeliCityName() + order.getDeliAreaName() + order.getDeliDetailAddr());
        pointMap.put("arriAddr", order.getArriProName() + order.getArriCityName() + order.getArriAreaName() + order.getArriDetailAddr());

        //根据发货单获取定位轨迹
        CarLocus carLocus = new CarLocus();
        carLocus.setInvoiceId(orderId);
        List<CarLocus> carLocusList = carLocusMapper.selectCarLocusList(carLocus);

        //委托单状态为已提货时，获取车辆当前时间的位置
        if (InvoiceStatusEnum.PICK_UP.getValue().equals(order.getVbillstatus())) {
            Entrust entrust = new Entrust();
            entrust.setOrderno(orderId);
            List<Entrust> entrustList = entrustMapper.selectEntrustList(entrust);
            for (int i = 0; i < entrustList.size(); i++) {
                if (EntrustStatusEnum.PICK_UP.getValue().equals(entrustList.get(i).getVbillstatus())) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(new Date());
                    calendar.add(Calendar.MINUTE, -2);
                    Date startTime = calendar.getTime();
                    calendar.add(Calendar.MINUTE, 2);
                    Date endTime = calendar.getTime();

                    //根据车牌号，时间段查询车辆位置
                    CarLocus carLocation = traceService.saveCarLocation(entrustList.get(i).getEntrustId(), startTime, endTime,"order","");

                    if (null != carLocation) {
                        if (carLocation != null) {
                            carLocusList.add(carLocation);
                        }
                    }
                }
            }
        }
        map.put("pointMap", pointMap);
        map.put("carLocusList", carLocusList);
        return map;
    }

    /**
     * 查询路线
     *
     * @param orderId
     * @return
     */
    @Override
    public List<AddressVO> selectPath(String orderId) {
        List<String> list = entrustMapper.selectSegmentVbillnoList(orderId);
        List<String> sementNoList = GetTransLineUtil.filterTranLine(list);
        System.out.print(sementNoList);
        String[] sementVbillnoList = list.toArray(new String[sementNoList.size()]);
        Map<String, Object> map = new HashMap<>();
        map.put("sementVbillnoList", sementVbillnoList);

        //获取委托单明细,填写高德地图起始到达地点，城市
        List<AddressVO> pathList = entrustMapper.selectEntrustArrivalBySegmentNo(map);
        for (int i = 0; i < pathList.size(); i++) {
            if (null != pathList.get(i).getArriveTime()) {
                pathList.get(i).setArriveDate(pathList.get(i).getArriveTime().substring(5, 10));
                pathList.get(i).setArriveTime(pathList.get(i).getArriveTime().substring(11, 16));
            }
        }
        List<AddressVO> addressVOList = entrustMapper.selectEntrustByInvoiceId(orderId);
        if (null != addressVOList && addressVOList.size() > 0) {
            addressVOList.get(0).setArriveDate(addressVOList.get(0).getArriveTime().substring(5, 10));
            addressVOList.get(0).setArriveTime(addressVOList.get(0).getArriveTime().substring(11, 16));
            pathList.add(addressVOList.get(0));
        }
        return pathList;
    }

    /**
     * 查询订单信息
     *
     * @param orderId
     * @return
     */
    @Override
    public Order selectOrderInfoById(String orderId) {
        return orderMapper.selectOrderById(orderId);
    }

    /**
     * 评价统计
     * @param evaluateTotalVO
     * @return
     */
    @Override
    public List<EvaluateTotalVO> selectEvaluateTotal(EvaluateTotalVO evaluateTotalVO) {
        return orderMapper.selectEvaluateTotal(evaluateTotalVO);
    }


/**
 * 导入
 *
 * @param  发货单
 * @return
 */
 /*   @Transactional(rollbackFor = Exception.class)
    @Override
    public String importOrder(List<OrderGoods> goodsList) {
        if (StringUtils.isNull(goodsList) || goodsList.size() == 0) {
            throw new BusinessException("导入数据不能为空！");
        }

        Set<String> set = new HashSet<>();
        for (OrderGoods good : goodsList) {
            set.add(good.getCustOrderno());
        }
        Order order = new Order();
        String msg = "";
        boolean flag = true;
        try {
            for (String s : set) {
                String orderId = IdUtil.randomUUID();
                OrderGoods good = new OrderGoods();
                Double numCount = 0.00;
                Double weightCount = 0.00;
                Double volumeCount = 0.00;

                for (int i = 0; i < goodsList.size(); i++) {
                    if (goodsList.get(i).getCustOrderno().equals(s)) {
                        good = goodsList.get(i);
                        String id = IdUtil.randomUUID();
                        goodsList.get(i).setInvPackGoodsId(id);
                        goodsList.get(i).setInvoiceId(orderId);
                        numCount += goodsList.get(i).getNum();
                        weightCount += goodsList.get(i).getWeight();
                        volumeCount += goodsList.get(i).getVolume();
                        goodsList.get(i).setRegScrId("order");
                        int row = orderMapper.insertOrderGoods(goodsList.get(i));
                        if (row == 0) {
                            flag = false;
                            break;
                        }
                    }
                }
                order.setCustOrderno(good.getCustOrderno());
                order.setCustName(good.getCustName());
                order.setInvoiceId(orderId);
                order.setNumCount(numCount);
                order.setWeightCount(weightCount);
                order.setVolumeCount(volumeCount);
                //删除标记 0 未删除
                order.setDelFlag(0);
                //发货单新建
                order.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
                order.setArriAddrName(good.getArriAddrName());
                order.setDeliAddrName(good.getDeliAddrName());
                order.setBalaName(good.getBalaName());
                order.setReqArriDate(good.getReqArriDate());
                order.setReqDeliDate(good.getReqArriDate());
                Address address = addressMapper.selectAddressByName(good.getDeliAddrName());
                order.setDeliProvinceId(address.getProvinceId());
                order.setDeliCityId(address.getCityId());
                order.setDeliAreaId(address.getAreaId());
                order.setDeliDetailAddr(address.getDetailAddr());
                order.setDeliProName(address.getProvinceName());
                order.setDeliCityName(address.getCityName());
                order.setDeliAreaName(address.getAreaName());
                order.setDeliAddrCode(address.getAddrCode());
                order.setArriProvinceId(address.getProvinceId());
                order.setArriCityId(address.getCityId());
                order.setArriAreaId(address.getAreaId());
                order.setArriDetailAddr(address.getDetailAddr());
                order.setArriProName(address.getProvinceName());
                order.setArriCityName(address.getCityName());
                order.setArriAreaName(address.getAreaName());
                order.setArriAddrCode(address.getAddrCode());
                order.setCarLen(good.getCarLen());
                order.setCarType(good.getCarType());
                //查询客户信息
                ClientVO client = orderMapper.selectCustById(shiroUtils.getUserId());
                order.setCustomerId(client.getCustomerId());
                //结算公司
                String balaCorpId = good.getBalaCorpId();
                //设置发货单号 逻辑：结算公司编号 + 当前年月日 + 四位Sequence。例如：MY201909150001
                String seqInvoice = StrUtil.fillBefore(orderMapper.getSeqInvoice() + "", '0', 4);
                order.setVbillno(balaCorpId + DateUtil.format(new Date(), "yyyyMMdd") + seqInvoice);
                order.setRegScrId("order");
                int row = orderMapper.insertOrder(order);
                if (row == 0) {
                    flag = false;
                    break;
                }
            }
        } catch (Exception e) {
            flag = false;
            log.error(msg, e);
        } finally {
            if (flag) {
                msg = "数据导入成功";
            } else {
                throw new BusinessException("数据导入失败");
            }
        }
        return msg;
    }
*/

    @Override
    public Map<String, Object> getActDeliArriReceiptTime(String orderId) {
        return orderMapper.getActDeliArriReceiptTime(orderId);
    }

    @Override
    public List<String> listReceiptPicList(String orderId) {
        return orderMapper.listReceiptPicList(orderId);
    }

    /**
     * 新增承运商
     * @param carrier
     * @return
     */
    @Override
    @Transactional
    public AjaxResult saveCarrier(Carrier carrier) {

        carrier.setCarrierId(IdUtil.simpleUUID());
        //默认通过
        carrier.setCheckStatus(1);
        carrier.setPushCheckMan(shiroUtils.getSysUser().getUserName());
        carrier.setPushCheckDate(new Date());
        carrier.setBlongUserId(shiroUtils.getUserId().toString());
        carrier.setBlongDate(new Date());
        carrier.setRegScrId("carrier-outer");
        carrierMapper.insertCarrier(carrier);

        // 联系人名称为用户名
        CarrierUser carrierUser = new CarrierUser();
        carrierUser.setUserName(carrier.getCarrName());
        carrierUser.setPhone(carrier.getPhone());
        carrierUser.setLoginName(carrier.getPhone());
        carrierUser.setCarrierId(carrier.getCarrierId());
        carrierUser.setUserType(MobileUserType.PERSONAL.getValue());
        carrierUser.setRegDate(new Date());
        carrierUser.setRegScrId("carrier-outer");
        carrierUser.setCorScrId("carrier-outer");
        SecureRandomNumberGenerator secureRandom = new SecureRandomNumberGenerator();
        String hex = secureRandom.nextBytes(3).toHex();
        carrierUser.setSalt(hex);
        String password = Md5Utils.hash(carrierUser.getLoginName()
                + configService.selectConfigByKey("sys.user.initPassword") + hex);
        carrierUser.setPassword(password);
        mobileUserService.addCarrierAccount(carrierUser,"");
        return AjaxResult.success();
    }

    @Override
    public List<String> listPickUpPicList(String orderId) {
        return orderMapper.listPickUpPicList(orderId);
    }

    @Override
    public List<String> listArrivalPicList(String orderId) {
        return orderMapper.listArrivalPicList(orderId);
    }
}