package com.ruoyi.tms.service.owner;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.owner.Order;
import com.ruoyi.tms.domain.owner.OrderGoods;
import com.ruoyi.tms.vo.business.EvaluateTotalVO;
import com.ruoyi.tms.vo.owner.AddressVO;
import com.ruoyi.tms.vo.client.ClientVO;

import java.util.List;
import java.util.Map;

/**
 * 发货单 服务层
 *
 * <AUTHOR>
 * @date 2019-09-17
 */
public interface IOrderService {
    /**
     * 查询发货单信息
     *
     * @param orderId 发货单ID
     * @return 发货单信息
     */
    Map<String, Object> selectOrderById(String orderId, int flag);

    /**
     * 查询发货单列表
     *
     * @param order 发货单信息
     * @return 发货单集合
     */
    List<Order> selectOrderList(Order order);

    /**
     * 查询发货单列表
     *
     * @param client
     * @return 发货单
     */
    List<OrderGoods> selectOrderGoodsList(ClientVO client);

    /**
     * 新增发货单
     *
     * @param order 发货单信息
     * @return 结果
     */
    AjaxResult insertOrder(Order order);

    /**
     * 修改发货单
     *
     * @param order 发货单信息
     * @return 结果
     */
    int updateOrder(Order order);

    /**
     * 删除发货单信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    AjaxResult deleteOrderByIds(String ids);

    /**
     * 查询客户信息
     *
     * @param userId 登录Id
     * @return
     */
    ClientVO selectCustNameById(long userId);

    /**
     * 查询发货单异常
     *
     * @param orderId 发货单id
     * @param
     * @return
     */
    List<Map<String, Object>> selectExceptionByOrderId(String orderId);

    /**
     * 查询异常图片
     *
     * @param expId 异常id
     * @return
     */
    List<Map<String, Object>> selectExpPicByExpId(String expId);

    /**
     * 查询发货单单个异常详情
     *
     * @param expId 异常id
     * @param
     * @return
     */
    Map<String, Object> selectExpByExpId(String expId);

    /**
     * 新增评价
     *
     * @param params 评价
     * @return 结果
     */
    AjaxResult saveRating(Map<String, String> params) throws Exception;

    /**
     * 根据发货单ID查询已有的评价信息
     *
     * @param orderId 发货单ID
     * @return 结果
     */
    Map<String, Object> selectRatingInfoByOrderId(String orderId);

    /**
     * 导入
     *
     * @param goodsList 发货单
     * @return
     */
//    String importOrder(List<OrderGoods> goodsList);

    /**
     * 查询路线
     * @param orderId
     * @return
     */
    Map<String, Object> selectLocation(String orderId);

    /**
     * 查询路线
     * @param orderId
     * @return
     */
    List<AddressVO> selectPath(String orderId);

    /**
     * 查询订单信息
     * @param orderId
     * @return
     */
    Order selectOrderInfoById(String orderId);

    /**
     * 评价统计
     * @param evaluateTotalVO
     * @return
     */
    List<EvaluateTotalVO> selectEvaluateTotal(EvaluateTotalVO evaluateTotalVO);

    /**
     * 根据发货单id查询实际提到货时间，回单时间
     *
     * @param orderId
     * @return
     */
    Map<String, Object> getActDeliArriReceiptTime(String orderId);

    List<String> listReceiptPicList(String orderId);

    /**
     * 新增承运商
     * @param carrier
     * @return
     */
    AjaxResult saveCarrier(Carrier carrier);

    /**
     * 提货照片
     * @param orderId
     * @return
     */
    List<String> listPickUpPicList(String orderId);

    List<String> listArrivalPicList(String orderId);

    List<String> listPickUpPicListByEntrustId(String entrustId);

    List<String> listArrivalPicListByEntrustId(String entrustId);
}
