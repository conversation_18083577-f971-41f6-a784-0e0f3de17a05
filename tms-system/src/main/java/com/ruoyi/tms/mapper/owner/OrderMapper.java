package com.ruoyi.tms.mapper.owner;


import com.ruoyi.tms.domain.client.Client;
import com.ruoyi.tms.domain.invoice.InvPackGoods;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.owner.CustEvaluate;
import com.ruoyi.tms.domain.owner.CustEvaluateDtl;
import com.ruoyi.tms.domain.owner.Order;
import com.ruoyi.tms.domain.owner.OrderGoods;
import com.ruoyi.tms.vo.business.EvaluateTotalVO;
import com.ruoyi.tms.vo.client.ClientVO;
import com.ruoyi.tms.vo.owner.AddressVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 发货单 数据层
 *
 * <AUTHOR>
 * @date 2019-09-17
 */
public interface OrderMapper {
    /**
     * 查询发货单信息
     *
     * @param orderId 发货单ID
     * @return 发货单信息
     */
    Order selectOrderById(String orderId);

    /**
     * 查询发货单列表
     *
     * @param order 发货单信息
     * @return 发货单集合
     */
    List<Order> selectOrderList(Order order);

    /**
     * 新增发货单
     *
     * @param order 发货单信息
     * @return 结果
     */
    int insertOrder(Order order);

    /**
     * 修改发货单
     *
     * @param order 发货单信息
     * @return 结果
     */
    int updateOrder(Order order);

    /**
     * 新增发货单货物
     *
     * @param orderGoods 货物
     * @return
     */
    int insertOrderGoods(OrderGoods orderGoods);

    /**
     * 查询发货单货物
     *
     * @param orderId 货物
     * @return
     */
    List<OrderGoods> selectOrderGoods(String orderId);

    /**
     * 删除发货单商品
     *
     * @param invoiceId 需要删除的数据ID
     * @return
     */
    int deleteOrderGoodsById(String invoiceId);

    /**
     * 查询客户信息
     *
     * @param userId 登录Id
     * @return
     */
    ClientVO selectCustById(long userId);

    /**
     * 获取 Sequence  Invoice
     *
     * @return
     */
    int getSeqInvoice();

    /**
     * 查询发货单异常
     *
     * @param orderId 发货单id
     * @param
     * @return
     */
    List<Map<String, Object>> selectExceptionByOrderId(String orderId);

    /**
     * 查询异常图片
     *
     * @param expId 异常id
     * @return
     */
    List<Map<String, Object>> selectExpPicByExpId(String expId);

    /**
     * 查询发货单单个异常详情
     *
     * @param expId 异常id
     * @param
     * @return
     */
    Map<String, Object> selectExpByExpId(String expId);

    /**
     * 新增评价
     *
     * @param custEvaluate 评价
     * @return 结果
     */
    int insertCustEvaluate(CustEvaluate custEvaluate);

    /**
     * 查询字典表信息
     *
     * @param
     * @return 结果
     */
    List<Map<String, Object>> selectDictInfo();

    /**
     * 查询评价详情
     *
     * @param custEvaluateId 评价主表ID
     * @return 结果
     */
    List<CustEvaluateDtl> selectRatingInfoById(String custEvaluateId);

    /**
     * 新增评价详情
     *
     * @param custEvaluateDtl 评价详情
     * @return 结果
     */
    int insertCustEvaluateDtl(CustEvaluateDtl custEvaluateDtl);

    /**
     * 查询评价
     *
     * @param orderId 发货单ID
     * @return 结果
     */
    CustEvaluate selectEvaluate(String orderId);

    /**
     * 根据发货单id数组，查询不为 <状态id> 状态下的发货单list
     *
     * @param ids    id数组
     * @param status 新建状态id
     * @return
     */
    List<Order> getNoNewListByIds(@Param("ids") String[] ids, @Param("status") String status);

    /**
     * 根据发货单id 更新数据
     *
     * @param orderGoods
     * @return
     */
    int updateOrderGoodsByOrderId(OrderGoods orderGoods);

    /**
     * 批量逻辑删除，更新 del_flag 为 1
     *
     * @param ids
     * @param delUserid
     * @return
     */
    int deleteOrdersByIdsLogic(@Param("ids") String[] ids, @Param("delUserid") String delUserid, @Param("checkStatus") String checkStatus);

    /**
     * 查询发货单列表
     *
     * @param client
     * @return 发货单集合
     */
    List<OrderGoods> selectOrderGoodsList(ClientVO client);

    /**
     * 更新发货单是否评价状态
     *
     * @param  order 发货单信息
     * @return
     */
    int updateOrderIsRating(Order order);

    /**
     * 评价统计
     * @param evaluateTotalVO
     * @return
     */
    List<EvaluateTotalVO> selectEvaluateTotal(EvaluateTotalVO evaluateTotalVO);

    Map<String, Object> getActDeliArriReceiptTime(String orderId);

    List<String> listReceiptPicList(String orderId);

    List<String> listPickUpPicList(String orderId);

    List<String> listArrivalPicList(String orderId);

    List<String> listPickUpPicListByEntrustId(String entrustId);

    List<String> listArrivalPicListByEntrustId(String entrustId);
}