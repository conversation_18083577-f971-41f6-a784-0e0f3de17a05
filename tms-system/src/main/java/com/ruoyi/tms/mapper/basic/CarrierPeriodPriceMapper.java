package com.ruoyi.tms.mapper.basic;

import com.ruoyi.tms.domain.basic.CarrierPeriodPrice;
import com.ruoyi.tms.domain.basic.CarrierPeriodPriceExample;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarrierPeriodPriceMapper {
    int countByExample(CarrierPeriodPriceExample example);

    //int deleteByExample(CarrierPeriodPriceExample example);

    //int deleteByPrimaryKey(String id);

    int insert(CarrierPeriodPrice record);

    int insertSelective(CarrierPeriodPrice record);

    List<CarrierPeriodPrice> selectByExample(CarrierPeriodPriceExample example);

    CarrierPeriodPrice selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") CarrierPeriodPrice record, @Param("example") CarrierPeriodPriceExample example);

    int updateByExample(@Param("record") CarrierPeriodPrice record, @Param("example") CarrierPeriodPriceExample example);

    int updateByPrimaryKeySelective(CarrierPeriodPrice record);

    int updateByPrimaryKey(CarrierPeriodPrice record);

    int updateNecessary(CarrierPeriodPrice record);

    int deletePPAndDetail(@Param("carrierId") String carrierId, @Param("idList") List<String> deletedPeriodPriceIdList,
                          @Param("time") Date time, @Param("userId") String userId, @Param("batch") String batch, @Param("action") String action);

    int log1(@Param("carrierId") String carrierId, @Param("startArea") String startAddr, @Param("endArea") String endAddr,
             @Param("periodId") String periodId,
             @Param("goodsKind") String goodsKind, @Param("time") Date time,
             @Param("batch") String batch, @Param("userId") Long userId,
             @Param("num") BigDecimal num, @Param("numPre") BigDecimal numPre, @Param("action") String action);

    int log2(@Param("carrierId") String carrierId, @Param("startArea") String startAddr, @Param("endArea") String endAddr,
             @Param("goodsKind") String goodsKind, @Param("time") Date time,
             @Param("batch") String batch, @Param("userId") Long userId, @Param("num") BigDecimal num, @Param("numPre") BigDecimal numPre);

    int log3(@Param("carrierId") String carrierId, @Param("startArea") String startAddr, @Param("endArea") String endAddr,
             @Param("goodsKind") String goodsKind, @Param("time") Date time,
             @Param("batch") String batch, @Param("userId") Long userId, @Param("num") Integer num, @Param("numPre") Integer numPre);

    List<CarrierPeriodPrice> selectByCarrierId(@Param("carrierId") String carrierId, @Param("goodsKind") String goodsKind);
}