package com.ruoyi.tms.mapper.basic;

import com.ruoyi.tms.vo.basic.*;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

public interface ContractDriverSQLMapper {

    @Select({
            "<script>",
            " select a.id ",
            "        ,A.TMPLATE_ID   tmplateId",
            "        ,B.TMPLATE_TITLE tmplateTitle ",
            "        ,A.CONTRACT_CODE contractCode",
            "        ,A.PARTY_A   partyA",
            "        ,A.PARTY_A_ID   partyAId",
            "        ,A.PARTY_B   partyB",
            "        ,A.PARTY_B_ID   partyBId",
            "        ,A.CARRIER_ID   carrierId",
            "        ,A.CARRIER_NAME carrierName",
            "        ,A.CARRIER_ABBR carrierAbbr",
            "        ,A.SIGN_URL   signUrl",
            "        ,A.SIGN_DATE   signDate",
            "        ,A.START_DATE   startDate",
            "        ,<PERSON>.END_DATE   endDate",
            "        ,A.REG_USER_ID   regUserId",
            "        ,A.REG_DATE   regDate",
            "        ,A.COR_USER_ID   corUserId",
            "        ,A.COR_DATE   corDate",
            "        ,A.DEL_USER_ID   delUserId",
            "        ,A.DEL_DATE   delDate",
            "   from T_CONTRACT_DRIVER a",
            " left join t_contract_template b",
            "     on a.tmplate_id = b.id",
            "  where a.del_flag = 0",
            "<if test='carrierId != null and carrierId.length > 0' >",
            "    and a.carrier_id = #{carrierId} ",
            "</if>",
            "",
            "",
            "",
            "</script>"
    })
    List<ContractDriverVO> selectContractDriverList(ContractDriverVO vo);

    @Update({
            "<script>",
            " update T_CONTRACT_DRIVER set del_flag = 1 where id in ",
            " <foreach collection='idArr' item='id' open='(' close=')' separator=','> ",
            " #{id} ",
            " </foreach>",
            "</script>"
    })
    int removes(@Param("idArr") String[] idArr);

    @Select({
            "<script>",
            " select count(1) from t_contract_driver a",
            " where a.carrier_id = #{carrierId} ",
            "   and (( #{startDate} between a.start_date and a.end_date )",
            "    or ( #{endDate} between a.start_date and a.end_date))",
            "   and a.del_flag = 0 ",
            "",
            "",
            "",
            "",
            "",
            "",
            "",
            "",
            "</script>",
    })
    int isExist(@Param("carrierId")String carrierId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}