<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.tms.mapper.owner.OrderMapper">

    <resultMap type="Order" id="OrderResult">
        <result property="invoiceId" column="invoice_id"/>
        <result property="vbillno" column="vbillno"/>
        <result property="custOrderno" column="cust_orderno"/>
        <result property="vbillstatus" column="vbillstatus"/>
        <result property="groupId" column="group_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="balaCustomerId" column="bala_customer_id"/>
        <result property="balaCorpId" column="bala_corp_id"/>
        <result property="balaDept" column="bala_dept"/>
        <result property="stationDept" column="station_dept"/>
        <result property="residentsId" column="residents_id"/>
        <result property="transLineId" column="trans_line_id"/>
        <result property="dispatcherId" column="dispatcher_id"/>
        <result property="balaType" column="bala_type"/>
        <result property="urgentLevel" column="urgent_level"/>
        <result property="ifBilling" column="if_billing"/>
        <result property="reqDeliDate" column="req_deli_date"/>
        <result property="reqArriDate" column="req_arri_date"/>
        <result property="orderDate" column="order_date"/>
        <result property="psndoc" column="psndoc"/>
        <result property="salesDept" column="sales_dept"/>
        <result property="memo" column="memo"/>
        <result property="deliveryId" column="delivery_id"/>
        <result property="deliProvinceId" column="deli_province_id"/>
        <result property="deliCityId" column="deli_city_id"/>
        <result property="deliAreaId" column="deli_area_id"/>
        <result property="deliDetailAddr" column="deli_detail_addr"/>
        <result property="deliContact" column="deli_contact"/>
        <result property="deliMobile" column="deli_mobile"/>
        <result property="deliEmail" column="deli_email"/>
        <result property="arrivalId" column="arrival_id"/>
        <result property="arriProvinceId" column="arri_province_id"/>
        <result property="arriCityId" column="arri_city_id"/>
        <result property="arriAreaId" column="arri_area_id"/>
        <result property="arriDetailAddr" column="arri_detail_addr"/>
        <result property="arriContact" column="arri_contact"/>
        <result property="arriMobile" column="arri_mobile"/>
        <result property="arriEmail" column="arri_email"/>
        <result property="ifBackbill" column="if_backbill"/>
        <result property="backbillNum" column="backbill_num"/>
        <result property="ifInsReceipt" column="if_ins_receipt"/>
        <result property="receiptAmount" column="receipt_amount"/>
        <result property="receiptMemo" column="receipt_memo"/>
        <result property="insuranceAppendixId" column="insurance_appendix_id"/>
        <result property="insuranceNo" column="insurance_no"/>
        <result property="insuranceCompany" column="insurance_company"/>
        <result property="numCount" column="num_count"/>
        <result property="weightCount" column="weight_count"/>
        <result property="volumeCount" column="volume_count"/>
        <result property="costAmount" column="cost_amount"/>
        <result property="carLen" column="car_len"/>
        <result property="carType" column="car_type"/>
        <result property="unconfirmType" column="unconfirm_type"/>
        <result property="unconfirmMemo" column="unconfirm_memo"/>
        <result property="billingCorp" column="billing_corp"/>
        <result property="deliProName" column="deli_pro_name"/>
        <result property="deliCityName" column="deli_city_name"/>
        <result property="deliAreaName" column="deli_area_name"/>
        <result property="deliAddrCode" column="deli_addr_code"/>
        <result property="deliAddrName" column="deli_addr_name"/>
        <result property="arriProName" column="arri_pro_name"/>
        <result property="arriCityName" column="arri_city_name"/>
        <result property="arriAreaName" column="arri_area_name"/>
        <result property="arriAddrCode" column="arri_addr_code"/>
        <result property="arriAddrName" column="arri_addr_name"/>
        <result property="custCode" column="cust_code"/>
        <result property="custName" column="cust_name"/>
        <result property="balaCode" column="bala_code"/>
        <result property="balaName" column="bala_name"/>
        <result property="transCode" column="trans_code"/>
        <result property="transName" column="trans_name"/>
        <result property="urgentLevelName" column="urgent_level_name"/>
        <result property="carLenName" column="car_len_name"/>
        <result property="carTypeName" column="car_type_name"/>
        <result property="confirmUserid" column="confirm_userid"/>
        <result property="confirmDate" column="confirm_date"/>
        <result property="unconfirmUserid" column="unconfirm_userid"/>
        <result property="unconfirmDate" column="unconfirm_date"/>
        <result property="closeNote" column="close_note"/>
        <result property="transLineName" column="trans_line_name"/>
        <result property="groupName" column="group_name"/>
        <result property="dispatcherName" column="dispatcher_name"/>
        <result property="custAbbr" column="cust_abbr"/>
        <result property="appDeliContact" column="app_deli_contact"/>
        <result property="appDeliMobile" column="app_deli_mobile"/>
        <result property="srcType" column="src_type"/>
        <result property="isClose" column="is_close"/>
        <result property="isRating" column="is_rating"/>
        <result property="isNtocc" column="is_ntocc"/>
        <result property="businesstypename" column="businesstypename"/>
        <result property="corDate"    column="cor_date"    />
        <result property="segmentStatus"    column="segment_status"    />
    </resultMap>

    <resultMap type="ClientVO" id="ClientVOResult">
        <result property="customerId" column="customer_id"/>
        <result property="custCode" column="cust_code"/>
        <result property="custName" column="cust_name"/>
        <result property="custAbbr" column="cust_abbr"/>
        <result property="balaCorp" column="bala_corp"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="salesDept" column="sales_dept"/>
        <result property="stationDept" column="station_dept"/>
        <result property="psndoc" column="psndoc"/>
        <result property="balaDept" column="bala_dept"/>
        <result property="billingCorp" column="BILLING_CORP"/>
        <result property="appDeliContact" column="APP_DELI_CONTACT"/>
        <result property="appDeliMobile" column="APP_DELI_MOBILE"/>
    </resultMap>

    <resultMap type="OrderGoods" id="OrderGoodsResult">
        <result property="invPackGoodsId" column="inv_pack_goods_id"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="goodsType" column="goods_type"/>
        <result property="goodsId" column="goods_id"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="goodsName" column="goods_name"/>
        <result property="num" column="num"/>
        <result property="weight" column="weight"/>
        <result property="volume" column="volume"/>
        <result property="packId" column="pack_id"/>
        <result property="goodsTypeName" column="goods_type_name"/>
        <result property="custOrderno" column="cust_orderno"/>
    </resultMap>

    <resultMap type="CustEvaluate" id="CustEvaluateResult">
        <result property="custEvaluateId" column="cust_evaluate_id"/>
        <result property="memo" column="memo"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="invoiceNo" column="invoice_no"/>
    </resultMap>

    <resultMap type="CustEvaluateDtl" id="CustEvaluateDtlResult">
        <result property="custEvaluateDtlId" column="cust_evaluate_dtl_id"/>
        <result property="custEvaluateId" column="cust_evaluate_id"/>
        <result property="evaluateType" column="evaluate_type"/>
        <result property="dispatcherGrade" column="dispatcher_grade"/>
    </resultMap>

    <sql id="selectEntrustExpVo">
    select entrust_exp_id,
           entrust_id,
           exp_type,
           tracking_status,
           exp_occ_time,
           est_arrival_time,
           if_visible_to_cust,
           appendix_id,
           note,
           carrier_id,
           carno_id,
           driver_name,
           driver_mobile
      from t_entrust_exp
    </sql>

    <sql id="selectOrderVo">
        select invoice_id,
                del_flag,
                del_userid,
                del_date,
                vbillno,
                cust_orderno,
                vbillstatus,
                group_id,
                customer_id,
                bala_customer_id,
                bala_corp_id,
                bala_dept,
                station_dept,
                residents_id,
                trans_line_id,
                dispatcher_id,
                bala_type,
                urgent_level,
                if_billing,
                req_deli_date,
                req_arri_date,
                order_date,
                psndoc,
                sales_dept,
                memo,
                delivery_id,
                deli_province_id,
                deli_city_id,
                deli_area_id,
                deli_detail_addr,
                deli_contact,
                deli_mobile,
                deli_email,
                arrival_id,
                arri_province_id,
                arri_city_id,
                arri_area_id,
                arri_detail_addr,
                arri_contact,
                arri_mobile,
                arri_email,
                if_backbill,
                backbill_num,
                if_ins_receipt,
                receipt_amount,
                receipt_memo,
                insurance_appendix_id,
                insurance_no,
                insurance_company,
                num_count,
                weight_count,
                volume_count,
                cost_amount,
                car_len,
                car_type,
                unconfirm_type,
                unconfirm_memo,
                billing_corp,
                deli_pro_name,
                deli_city_name,
                deli_area_name,
                deli_addr_code,
                deli_addr_name,
                arri_pro_name,
                arri_city_name,
                arri_area_name,
                arri_addr_code,
                arri_addr_name,
                cust_code,
                cust_name,
                bala_code,
                bala_name,
                trans_code,
                trans_name,
                urgent_level_name,
                car_len_name,
                car_type_name,
                confirm_userid,
                confirm_date,
                unconfirm_userid,
                unconfirm_date,
                close_note,
                reg_user_id,
                reg_user_name,
                reg_date,
                cor_user_id,
                cor_user_name,
                cor_date,
                reg_scr_id,
                cor_scr_id,
                trans_line_name,
                group_name,
                dispatcher_name,
                cust_abbr,
                app_deli_contact,
                app_deli_mobile,
                src_type,
                is_close,
                is_rating,
                is_ntocc,
                 businesstypename
         from t_invoice
    </sql>

    <sql id="selectOrderGoodsVo">
        select inv_pack_goods_id,
       goods_type_name,
       invoice_id,
       goods_type,
       goods_id,
       goods_code,
       goods_name,
       num,
       weight,
       volume,
       pack_id,
       goods_type_name,
               cust_orderno
      from t_inv_pack_goods
    </sql>

    <sql id="selectCustEvaluateVo">
    select cust_evaluate_id,
           memo,
           invoice_id,
           invoice_no
      from t_cust_evaluate
    </sql>

    <sql id="selectCustEvaluateDtlVo">
    select cust_evaluate_dtl_id,
           cust_evaluate_id,
           evaluate_type,
           dispatcher_grade
      from t_cust_evaluate_dtl
    </sql>

    <!-- 查询订单列表-->
    <select id="selectOrderList" parameterType="Order" resultMap="OrderResult">
        select invoice_id,
            vbillno,
            cust_orderno,
            vbillstatus,
            cust_name,
            req_deli_date,
            req_arri_date,
            car_len,
            car_type,
            memo,
            deli_contact,
            deli_addr_name,
            deli_pro_name,
            deli_city_name,
            deli_area_name,
            deli_detail_addr,
            arri_addr_name,
            arri_pro_name,
            arri_city_name,
            arri_area_name,
            arri_detail_addr,
            num_count,
            weight_count,
            volume_count,
            bala_corp_Id,
            is_rating,
            trans_code,
            segment_status
        from t_invoice
        <where>
            <!--发货单编号-->
            <if test="vbillno != null  and vbillno != '' ">
                <bind name="vbillno" value="vbillno+'%'"/>
                and (vbillno like trim(#{vbillno}) or cust_orderno like trim(#{vbillno}) )
            </if>
            <!--客户发货单号-->
            <if test="custOrderno != null  and custOrderno != '' ">
                <bind name="custOrderno" value="custOrderno+'%'"/>
                and cust_orderno like trim(#{custOrderno})
            </if>

            <!--到货地址名称-->
            <if test="arriAddrName != null  and arriAddrName != '' ">
                <bind name="arriAddrName" value="arriAddrName+'%'"/>
                and arri_addr_name like trim(#{arriAddrName})
            </if>

            <!--发货单状态-->
            <if test="vbillstatus != null  and vbillstatus != '' and vbillstatus.indexOf(',') != -1">
                and vbillstatus in
                <foreach item="item" index="index" collection="vbillstatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vbillstatus != null  and vbillstatus != '' and vbillstatus.indexOf(',') == -1">
                and vbillstatus = #{vbillstatus}
            </if>
            <!--要求提货日期-->
            <if test="reqDeliDate != null ">
                and req_deli_date = #{reqDeliDate}
            </if>
            <if test="reqDeliDateRange != null">
                and req_deli_date >= to_date(#{reqDeliDateRange[0],javaType=string},'YYYY-MM-DD')
                and req_deli_date &lt; to_date(#{reqDeliDateRange[1],javaType=string},'YYYY-MM-DD') + 1
            </if>
            <!--要求到货日期-->
            <if test="reqArriDate != null ">
                and req_arri_date = #{reqArriDate}
            </if>
            <if test="reqArriDateRange != null">
                and req_arri_date >= to_date(#{reqArriDateRange[0],javaType=string},'YYYY-MM-DD')
                and req_arri_date &lt; to_date(#{reqArriDateRange[1],javaType=string},'YYYY-MM-DD') + 1
            </if>
            <if test="reqDeliDateStart != null  and reqDeliDateStart != ''">
                and req_deli_date <![CDATA[ >= ]]> to_date(#{reqDeliDateStart},'yyyy-mm-dd')
            </if>
            <if test="reqDeliDateEnd != null  and reqDeliDateEnd != ''">
                and req_deli_date <![CDATA[ < ]]> to_date(#{reqDeliDateEnd},'yyyy-mm-dd') + 1
            </if>

            <!--车长-->
            <if test="carLen != null and carLen !='' and carLen.indexOf(',') != -1">
                and car_len in
                <foreach item="item" index="index" collection="carLen.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="carLen != null and carLen !=''and carLen.indexOf(',') == -1 ">
                and car_len = #{carLen}
            </if>
            <!--车型-->
            <if test="carType != null  and carType != '' and carType.indexOf(',') != -1">
                and car_type in
                <foreach item="item" index="index" collection="carType.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="carType != null  and carType != '' and carType.indexOf(',') == -1 ">
                and car_type = #{carType}
            </if>
            <!--结算公司-->
            <if test="balaCorpId != null  and balaCorpId != '' ">and bala_corp_id = #{balaCorpId}</if>
            <!--结算客户-->
            <if test="balaName != null  and balaName != '' ">
                <bind name="balaName" value="balaName+'%'"/>
                and bala_name like trim(#{balaName})
            </if>
            <if test="customerId != null  and customerId != '' ">
                and customer_id = #{customerId}
            </if>
            <if test="deliProvinceId != null  and deliProvinceId != '' ">and deli_province_id = #{deliProvinceId}</if>
            <if test="deliCityId != null  and deliCityId != '' ">and deli_city_id = #{deliCityId}</if>
            <if test="deliAreaId != null  and deliAreaId != '' ">and deli_area_id = #{deliAreaId}</if>
            <if test="deliDetailAddr != null  and deliDetailAddr != '' ">
                <bind name="deliDetailAddrLike" value="'%'+deliDetailAddr+'%'"/>
                and deli_detail_addr like #{deliDetailAddrLike}
            </if>
            <if test="arriProvinceId != null  and arriProvinceId != '' ">and arri_province_id = #{arriProvinceId}</if>
            <if test="arriCityId != null  and arriCityId != '' ">and arri_city_id = #{arriCityId}</if>
            <if test="arriAreaId != null  and arriAreaId != '' ">and arri_area_id = #{arriAreaId}</if>
            <if test="arriDetailAddr != null  and arriDetailAddr != '' ">
                <bind name="arriDetailAddrLike" value="'%'+arriDetailAddr+'%'"/>
                and arri_detail_addr like #{arriDetailAddrLike}
            </if>
            <if test="arriUserId != null">
                and arri_user_id = #{arriUserId}
            </if>

            and del_flag=0
        </where>
        order by req_deli_date desc
    </select>

    <!-- 查询订单详情-->
    <select id="selectOrderById" parameterType="String" resultMap="OrderResult">
        <include refid="selectOrderVo"/>
        where invoice_id = #{invoiceId}
        and del_flag = 0
    </select>

    <!-- 新增订单-->
    <insert id="insertOrder" parameterType="Order">
        insert into t_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null  and invoiceId != ''  ">invoice_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delUserid != null  and delUserid != ''  ">del_userid,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="vbillno != null  and vbillno != ''  ">vbillno,</if>
            <if test="custOrderno != null  and custOrderno != ''  ">cust_orderno,</if>
            <if test="vbillstatus != null  and vbillstatus != ''  ">vbillstatus,</if>
            <if test="groupId != null  and groupId != ''  ">group_id,</if>
            <if test="customerId != null  and customerId != ''  ">customer_id,</if>
            <if test="balaCustomerId != null  and balaCustomerId != ''  ">bala_customer_id,</if>
            <if test="balaCorpId != null  and balaCorpId != ''  ">bala_corp_id,</if>
            <if test="balaDept != null  and balaDept != ''  ">bala_dept,</if>
            <if test="stationDept != null  and stationDept != ''  ">station_dept,</if>
            <if test="residentsId != null  and residentsId != ''  ">residents_id,</if>
            <if test="transLineId != null  and transLineId != ''  ">trans_line_id,</if>
            <if test="dispatcherId != null  and dispatcherId != ''  ">dispatcher_id,</if>
            <if test="balaType != null  and balaType != ''  ">bala_type,</if>
            <if test="urgentLevel != null  and urgentLevel != ''  ">urgent_level,</if>
            <if test="ifBilling != null  and ifBilling != ''  ">if_billing,</if>
            <if test="reqDeliDate != null  ">req_deli_date,</if>
            <if test="reqArriDate != null  ">req_arri_date,</if>
            <if test="regDate != null  ">order_date,</if>
            <if test="psndoc != null  and psndoc != ''  ">psndoc,</if>
            <if test="salesDept != null  and salesDept != ''  ">sales_dept,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="deliveryId != null  and deliveryId != ''  ">delivery_id,</if>
            <if test="deliProvinceId != null  and deliProvinceId != ''  ">deli_province_id,</if>
            <if test="deliCityId != null  and deliCityId != ''  ">deli_city_id,</if>
            <if test="deliAreaId != null  and deliAreaId != ''  ">deli_area_id,</if>
            <if test="deliDetailAddr != null  and deliDetailAddr != ''  ">deli_detail_addr,</if>
            <if test="deliContact != null  and deliContact != ''  ">deli_contact,</if>
            <if test="deliMobile != null  and deliMobile != ''  ">deli_mobile,</if>
            <if test="deliEmail != null  and deliEmail != ''  ">deli_email,</if>
            <if test="arrivalId != null  and arrivalId != ''  ">arrival_id,</if>
            <if test="arriProvinceId != null  and arriProvinceId != ''  ">arri_province_id,</if>
            <if test="arriCityId != null  and arriCityId != ''  ">arri_city_id,</if>
            <if test="arriAreaId != null  and arriAreaId != ''  ">arri_area_id,</if>
            <if test="arriDetailAddr != null  and arriDetailAddr != ''  ">arri_detail_addr,</if>
            <if test="arriContact != null  and arriContact != ''  ">arri_contact,</if>
            <if test="arriMobile != null  and arriMobile != ''  ">arri_mobile,</if>
            <if test="arriEmail != null  and arriEmail != ''  ">arri_email,</if>
            <if test="ifBackbill != null  and ifBackbill != ''  ">if_backbill,</if>
            <if test="backbillNum != null  ">backbill_num,</if>
            <if test="ifInsReceipt != null  and ifInsReceipt != ''  ">if_ins_receipt,</if>
            <if test="receiptAmount != null  ">receipt_amount,</if>
            <if test="receiptMemo != null  and receiptMemo != ''  ">receipt_memo,</if>
            <if test="insuranceAppendixId != null  and insuranceAppendixId != ''  ">insurance_appendix_id,</if>
            <if test="insuranceNo != null  and insuranceNo != ''  ">insurance_no,</if>
            <if test="insuranceCompany != null  and insuranceCompany != ''  ">insurance_company,</if>
            <if test="numCount != null  ">num_count,</if>
            <if test="weightCount != null  ">weight_count,</if>
            <if test="volumeCount != null  ">volume_count,</if>
            <if test="costAmount != null  ">cost_amount,</if>
            <if test="carLen != null  and carLen != ''  ">car_len,</if>
            <if test="carType != null  and carType != ''  ">car_type,</if>
            <if test="unconfirmType != null  and unconfirmType != ''  ">unconfirm_type,</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo,</if>
            <if test="billingCorp != null  and billingCorp != ''  ">billing_corp,</if>
            <if test="deliProName != null  and deliProName != ''  ">deli_pro_name,</if>
            <if test="deliCityName != null  and deliCityName != ''  ">deli_city_name,</if>
            <if test="deliAreaName != null  and deliAreaName != ''  ">deli_area_name,</if>
            <if test="deliAddrCode != null  and deliAddrCode != ''  ">deli_addr_code,</if>
            <if test="deliAddrName != null  and deliAddrName != ''  ">deli_addr_name,</if>
            <if test="arriProName != null  and arriProName != ''  ">arri_pro_name,</if>
            <if test="arriCityName != null  and arriCityName != ''  ">arri_city_name,</if>
            <if test="arriAreaName != null  and arriAreaName != ''  ">arri_area_name,</if>
            <if test="arriAddrCode != null  and arriAddrCode != ''  ">arri_addr_code,</if>
            <if test="arriAddrName != null  and arriAddrName != ''  ">arri_addr_name,</if>
            <if test="custCode != null  and custCode != ''  ">cust_code,</if>
            <if test="custName != null  and custName != ''  ">cust_name,</if>
            <if test="balaCode != null  and balaCode != ''  ">bala_code,</if>
            <if test="balaName != null  and balaName != ''  ">bala_name,</if>
            <if test="transCode != null  and transCode != ''  ">trans_code,</if>
            <if test="transName != null  and transName != ''  ">trans_name,</if>
            <if test="urgentLevelName != null  and urgentLevelName != ''  ">urgent_level_name,</if>
            <if test="carLenName != null  and carLenName != ''  ">car_len_name,</if>
            <if test="carTypeName != null  and carTypeName != ''  ">car_type_name,</if>
            <if test="confirmUserid != null  and confirmUserid != ''  ">confirm_userid,</if>
            <if test="confirmDate != null  ">confirm_date,</if>
            <if test="unconfirmUserid != null  and unconfirmUserid != ''  ">unconfirm_userid,</if>
            <if test="unconfirmDate != null  ">unconfirm_date,</if>
            <if test="closeNote != null  and closeNote != ''  ">close_note,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regUserName != null  and regUserName != ''  ">reg_user_name,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regUserId != null  and regUserId != ''  ">cor_user_id,</if>
            <if test="corUserName != null  and corUserName != ''  ">cor_user_name,</if>
            <if test="regDate != null  ">cor_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="transLineName != null  and transLineName != ''  ">trans_line_name,</if>
            <if test="groupName != null  and groupName != ''  ">group_name,</if>
            <if test="dispatcherName != null  and dispatcherName != ''  ">dispatcher_name,</if>
            <if test="custAbbr != null  and custAbbr != ''  ">cust_abbr,</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">app_deli_contact,</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">app_deli_mobile,</if>
            <if test="srcType != null  and srcType != ''  ">src_type,</if>
            <if test="isClose != null  ">is_close,</if>
            <if test="isRating != null  ">is_rating,</if>
            <if test="isNtocc != null  and isNtocc != ''  ">is_ntocc,</if>
            <if test="businesstypename != null  and businesstypename != ''  ">businesstypename,</if>
            <if test="isMultiple != null">is_multiple,</if>
            <if test="isFleetData != null and isFleetData != ''">is_fleet_data,</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">is_fleet_assign,</if>
            <if test="segmentStatus != null">segment_status,</if>
            <if test="isAddReceCheck != null">is_add_rece_check,</if>
            <if test="receivableWriteOffStatus != null and receivableWriteOffStatus != ''">receivable_write_off_status,</if>
            <if test="goodsName != null and goodsName != ''">goods_name,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null  and invoiceId != ''  ">#{invoiceId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delUserid != null  and delUserid != ''  ">#{delUserid},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="vbillno != null  and vbillno != ''  ">#{vbillno},</if>
            <if test="custOrderno != null  and custOrderno != ''  ">#{custOrderno},</if>
            <if test="vbillstatus != null  and vbillstatus != ''  ">#{vbillstatus},</if>
            <if test="groupId != null  and groupId != ''  ">#{groupId},</if>
            <if test="customerId != null  and customerId != ''  ">#{customerId},</if>
            <if test="balaCustomerId != null  and balaCustomerId != ''  ">#{balaCustomerId},</if>
            <if test="balaCorpId != null  and balaCorpId != ''  ">#{balaCorpId},</if>
            <if test="balaDept != null  and balaDept != ''  ">#{balaDept},</if>
            <if test="stationDept != null  and stationDept != ''  ">#{stationDept},</if>
            <if test="residentsId != null  and residentsId != ''  ">#{residentsId},</if>
            <if test="transLineId != null  and transLineId != ''  ">#{transLineId},</if>
            <if test="dispatcherId != null  and dispatcherId != ''  ">#{dispatcherId},</if>
            <if test="balaType != null  and balaType != ''  ">#{balaType},</if>
            <if test="urgentLevel != null  and urgentLevel != ''  ">#{urgentLevel},</if>
            <if test="ifBilling != null  and ifBilling != ''  ">#{ifBilling},</if>
            <if test="reqDeliDate != null  ">#{reqDeliDate},</if>
            <if test="reqArriDate != null  ">#{reqArriDate},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="psndoc != null  and psndoc != ''  ">#{psndoc},</if>
            <if test="salesDept != null  and salesDept != ''  ">#{salesDept},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="deliveryId != null  and deliveryId != ''  ">#{deliveryId},</if>
            <if test="deliProvinceId != null  and deliProvinceId != ''  ">#{deliProvinceId},</if>
            <if test="deliCityId != null  and deliCityId != ''  ">#{deliCityId},</if>
            <if test="deliAreaId != null  and deliAreaId != ''  ">#{deliAreaId},</if>
            <if test="deliDetailAddr != null  and deliDetailAddr != ''  ">#{deliDetailAddr},</if>
            <if test="deliContact != null  and deliContact != ''  ">#{deliContact},</if>
            <if test="deliMobile != null  and deliMobile != ''  ">#{deliMobile},</if>
            <if test="deliEmail != null  and deliEmail != ''  ">#{deliEmail},</if>
            <if test="arrivalId != null  and arrivalId != ''  ">#{arrivalId},</if>
            <if test="arriProvinceId != null  and arriProvinceId != ''  ">#{arriProvinceId},</if>
            <if test="arriCityId != null  and arriCityId != ''  ">#{arriCityId},</if>
            <if test="arriAreaId != null  and arriAreaId != ''  ">#{arriAreaId},</if>
            <if test="arriDetailAddr != null  and arriDetailAddr != ''  ">#{arriDetailAddr},</if>
            <if test="arriContact != null  and arriContact != ''  ">#{arriContact},</if>
            <if test="arriMobile != null  and arriMobile != ''  ">#{arriMobile},</if>
            <if test="arriEmail != null  and arriEmail != ''  ">#{arriEmail},</if>
            <if test="ifBackbill != null  and ifBackbill != ''  ">#{ifBackbill},</if>
            <if test="backbillNum != null  ">#{backbillNum},</if>
            <if test="ifInsReceipt != null  and ifInsReceipt != ''  ">#{ifInsReceipt},</if>
            <if test="receiptAmount != null  ">#{receiptAmount},</if>
            <if test="receiptMemo != null  and receiptMemo != ''  ">#{receiptMemo},</if>
            <if test="insuranceAppendixId != null  and insuranceAppendixId != ''  ">#{insuranceAppendixId},</if>
            <if test="insuranceNo != null  and insuranceNo != ''  ">#{insuranceNo},</if>
            <if test="insuranceCompany != null  and insuranceCompany != ''  ">#{insuranceCompany},</if>
            <if test="numCount != null  ">#{numCount},</if>
            <if test="weightCount != null  ">#{weightCount},</if>
            <if test="volumeCount != null  ">#{volumeCount},</if>
            <if test="costAmount != null  ">#{costAmount},</if>
            <if test="carLen != null  and carLen != ''  ">#{carLen},</if>
            <if test="carType != null  and carType != ''  ">#{carType},</if>
            <if test="unconfirmType != null  and unconfirmType != ''  ">#{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">#{unconfirmMemo},</if>
            <if test="billingCorp != null  and billingCorp != ''  ">#{billingCorp},</if>
            <if test="deliName != null  and deliName != ''  ">#{deliName},</if>
            <if test="deliProName != null  and deliProName != ''  ">#{deliProName},</if>
            <if test="deliCityName != null  and deliCityName != ''  ">#{deliCityName},</if>
            <if test="deliAreaName != null  and deliAreaName != ''  ">#{deliAreaName},</if>
            <if test="deliAddrCode != null  and deliAddrCode != ''  ">#{deliAddrCode},</if>
            <if test="deliAddrName != null  and deliAddrName != ''  ">#{deliAddrName},</if>
            <if test="arriName != null  and arriName != ''  ">#{arriName},</if>
            <if test="arriProName != null  and arriProName != ''  ">#{arriProName},</if>
            <if test="arriCityName != null  and arriCityName != ''  ">#{arriCityName},</if>
            <if test="arriAreaName != null  and arriAreaName != ''  ">#{arriAreaName},</if>
            <if test="arriAddrCode != null  and arriAddrCode != ''  ">#{arriAddrCode},</if>
            <if test="arriAddrName != null  and arriAddrName != ''  ">#{arriAddrName},</if>
            <if test="custCode != null  and custCode != ''  ">#{custCode},</if>
            <if test="custName != null  and custName != ''  ">#{custName},</if>
            <if test="balaCode != null  and balaCode != ''  ">#{balaCode},</if>
            <if test="balaName != null  and balaName != ''  ">#{balaName},</if>
            <if test="transCode != null  and transCode != ''  ">#{transCode},</if>
            <if test="transName != null  and transName != ''  ">#{transName},</if>
            <if test="urgentLevelName != null  and urgentLevelName != ''  ">#{urgentLevelName},</if>
            <if test="carLenName != null  and carLenName != ''  ">#{carLenName},</if>
            <if test="carTypeName != null  and carTypeName != ''  ">#{carTypeName},</if>
            <if test="confirmUserid != null  and confirmUserid != ''  ">#{confirmUserid},</if>
            <if test="confirmDate != null  ">#{confirmDate},</if>
            <if test="unconfirmUserid != null  and unconfirmUserid != ''  ">#{unconfirmUserid},</if>
            <if test="unconfirmDate != null  ">#{unconfirmDate},</if>
            <if test="closeNote != null  and closeNote != ''  ">#{closeNote},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regUserName != null  and regUserName != ''  ">#{regUserName},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="corUserName != null  and corUserName != ''  ">#{corUserName},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="transLineName != null  and transLineName != ''  ">#{transLineName},</if>
            <if test="groupName != null  and groupName != ''  ">#{groupName},</if>
            <if test="dispatcherName != null  and dispatcherName != ''  ">#{dispatcherName},</if>
            <if test="custAbbr != null  and custAbbr != ''  ">#{custAbbr},</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">#{appDeliContact},</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">#{appDeliMobile},</if>
            <if test="srcType != null  and srcType != ''  ">#{srcType},</if>
            <if test="isClose != null  ">#{isClose},</if>
            <if test="isRating != null  ">#{isRating},</if>
            <if test="isNtocc != null  and isNtocc != ''  ">#{isNtocc},</if>
            <if test="businesstypename != null  and businesstypename != ''  ">#{businesstypename},</if>
            <if test="isMultiple != null">#{isMultiple},</if>
            <if test="isFleetData != null and isFleetData != ''" >#{isFleetData},</if>
            <if test="isFleetAssign != null and isFleetAssign != ''">#{isFleetAssign},</if>
            <if test="segmentStatus != null">#{segmentStatus},</if>
            <if test="isAddReceCheck != null">#{isAddReceCheck},</if>
            <if test="receivableWriteOffStatus != null and receivableWriteOffStatus != ''">#{receivableWriteOffStatus},</if>
            <if test="goodsName != null and goodsName != ''">#{goodsName},</if>

        </trim>
    </insert>

    <!-- 修改订单-->
    <update id="updateOrder" parameterType="Order">
        update t_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delUserid != null  and delUserid != ''  ">del_userid = #{delUserid},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>

            <if test="vbillno != null  and vbillno != ''  ">vbillno = #{vbillno},</if>
            <if test="custOrderno != null ">cust_orderno = #{custOrderno},</if>
            <if test="vbillstatus != null  and vbillstatus != ''  ">vbillstatus = #{vbillstatus},</if>
            <if test="groupId != null  and groupId != ''  ">group_id = #{groupId},</if>
            <if test="customerId != null  and customerId != ''  ">customer_id = #{customerId},</if>
            <if test="balaCustomerId != null  and balaCustomerId != ''  ">bala_customer_id = #{balaCustomerId},</if>
            <if test="balaCorpId != null  and balaCorpId != ''  ">bala_corp_id = #{balaCorpId},</if>
            <if test="balaDept != null  and balaDept != ''  ">bala_dept = #{balaDept},</if>
            <if test="stationDept != null  and stationDept != ''  ">station_dept = #{stationDept},</if>
            <if test="residentsId != null  and residentsId != ''  ">residents_id = #{residentsId},</if>
            <if test="transLineId != null  and transLineId != ''  ">trans_line_id = #{transLineId},</if>
            <if test="dispatcherId != null  and dispatcherId != ''  ">dispatcher_id = #{dispatcherId},</if>
            <if test="balaType != null  and balaType != ''  ">bala_type = #{balaType},</if>
            <if test="urgentLevel != null  and urgentLevel != ''  ">urgent_level = #{urgentLevel},</if>
            <if test="ifBilling != null  and ifBilling != ''  ">if_billing = #{ifBilling},</if>
            <if test="reqDeliDate != null  ">req_deli_date = #{reqDeliDate},</if>
            <if test="reqArriDate != null  ">req_arri_date = #{reqArriDate},</if>
            <if test="orderDate != null  ">order_date = #{orderDate},</if>
            <if test="psndoc != null  and psndoc != ''  ">psndoc = #{psndoc},</if>
            <if test="salesDept != null  and salesDept != ''  ">sales_dept = #{salesDept},</if>
            <if test="memo != null ">memo = #{memo},</if>
            <if test="deliveryId != null  and deliveryId != ''  ">delivery_id = #{deliveryId},</if>
            <if test="deliProvinceId != null  and deliProvinceId != ''  ">deli_province_id = #{deliProvinceId},</if>
            <if test="deliCityId != null  and deliCityId != ''  ">deli_city_id = #{deliCityId},</if>
            <if test="deliAreaId != null  and deliAreaId != ''  ">deli_area_id = #{deliAreaId},</if>
            <if test="deliDetailAddr != null  and deliDetailAddr != ''  ">deli_detail_addr = #{deliDetailAddr},</if>
            <if test="deliContact != null  and deliContact != ''  ">deli_contact = #{deliContact},</if>
            <if test="deliMobile != null  and deliMobile != ''  ">deli_mobile = #{deliMobile},</if>
            <if test="deliEmail != null  and deliEmail != ''  ">deli_email = #{deliEmail},</if>
            <if test="arrivalId != null  and arrivalId != ''  ">arrival_id = #{arrivalId},</if>
            <if test="arriProvinceId != null  and arriProvinceId != ''  ">arri_province_id = #{arriProvinceId},</if>
            <if test="arriCityId != null  and arriCityId != ''  ">arri_city_id = #{arriCityId},</if>
            <if test="arriAreaId != null  and arriAreaId != ''  ">arri_area_id = #{arriAreaId},</if>
            <if test="arriDetailAddr != null  and arriDetailAddr != ''  ">arri_detail_addr = #{arriDetailAddr},</if>
            <if test="arriContact != null  and arriContact != ''  ">arri_contact = #{arriContact},</if>
            <if test="arriMobile != null  and arriMobile != ''  ">arri_mobile = #{arriMobile},</if>
            <if test="arriEmail != null  and arriEmail != ''  ">arri_email = #{arriEmail},</if>
            <if test="ifBackbill != null  and ifBackbill != ''  ">if_backbill = #{ifBackbill},</if>
            <if test="backbillNum != null  ">backbill_num = #{backbillNum},</if>
            <if test="ifInsReceipt != null  and ifInsReceipt != ''  ">if_ins_receipt = #{ifInsReceipt},</if>
            <if test="receiptAmount != null  ">receipt_amount = #{receiptAmount},</if>
            <if test="receiptMemo != null  and receiptMemo != ''  ">receipt_memo = #{receiptMemo},</if>
            <if test="insuranceAppendixId != null  and insuranceAppendixId != ''  ">insurance_appendix_id =
                #{insuranceAppendixId},
            </if>
            <if test="insuranceNo != null  and insuranceNo != ''  ">insurance_no = #{insuranceNo},</if>
            <if test="insuranceCompany != null  and insuranceCompany != ''  ">insurance_company = #{insuranceCompany},
            </if>
            <if test="numCount != null  ">num_count = #{numCount},</if>
            <if test="weightCount != null  ">weight_count = #{weightCount},</if>
            <if test="volumeCount != null  ">volume_count = #{volumeCount},</if>
            <if test="costAmount != null  ">cost_amount = #{costAmount},</if>
            <if test="carLen != null  and carLen != ''  ">car_len = #{carLen},</if>
            <if test="carType != null  and carType != ''  ">car_type = #{carType},</if>
            <if test="unconfirmType != null  and unconfirmType != ''  ">unconfirm_type = #{unconfirmType},</if>
            <if test="unconfirmMemo != null  and unconfirmMemo != ''  ">unconfirm_memo = #{unconfirmMemo},</if>
            <if test="billingCorp != null  and billingCorp != ''  ">billing_corp = #{billingCorp},</if>
            <if test="deliProName != null  and deliProName != ''  ">deli_pro_name = #{deliProName},</if>
            <if test="deliCityName != null  and deliCityName != ''  ">deli_city_name = #{deliCityName},</if>
            <if test="deliAreaName != null  and deliAreaName != ''  ">deli_area_name = #{deliAreaName},</if>
            <if test="deliAddrCode != null  and deliAddrCode != ''  ">deli_addr_code = #{deliAddrCode},</if>
            <if test="deliAddrName != null  and deliAddrName != ''  ">deli_addr_name = #{deliAddrName},</if>
            <if test="arriProName != null  and arriProName != ''  ">arri_pro_name = #{arriProName},</if>
            <if test="arriCityName != null  and arriCityName != ''  ">arri_city_name = #{arriCityName},</if>
            <if test="arriAreaName != null  and arriAreaName != ''  ">arri_area_name = #{arriAreaName},</if>
            <if test="arriAddrCode != null  and arriAddrCode != ''  ">arri_addr_code = #{arriAddrCode},</if>
            <if test="arriAddrName != null  and arriAddrName != ''  ">arri_addr_name = #{arriAddrName},</if>
            <if test="custCode != null  and custCode != ''  ">cust_code = #{custCode},</if>
            <if test="custName != null  and custName != ''  ">cust_name = #{custName},</if>
            <if test="balaCode != null  and balaCode != ''  ">bala_code = #{balaCode},</if>
            <if test="balaName != null  and balaName != ''  ">bala_name = #{balaName},</if>
            <if test="transCode != null  and transCode != ''  ">trans_code = #{transCode},</if>
            <if test="transName != null  and transName != ''  ">trans_name = #{transName},</if>
            <if test="urgentLevelName != null  and urgentLevelName != ''  ">urgent_level_name = #{urgentLevelName},</if>
            <if test="carLenName != null  and carLenName != ''  ">car_len_name = #{carLenName},</if>
            <if test="carTypeName != null  and carTypeName != ''  ">car_type_name = #{carTypeName},</if>
            <if test="confirmUserid != null  and confirmUserid != ''  ">confirm_userid = #{confirmUserid},</if>
            <if test="confirmDate != null  ">confirm_date = #{confirmDate},</if>
            <if test="unconfirmUserid != null  and unconfirmUserid != ''  ">unconfirm_userid = #{unconfirmUserid},</if>
            <if test="unconfirmDate != null  ">unconfirm_date = #{unconfirmDate},</if>
            <if test="closeNote != null  and closeNote != ''  ">close_note = #{closeNote},</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regUserName != null  and regUserName != ''  ">reg_user_name = #{regUserName},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corUserName != null  and corUserName != ''  ">cor_user_name = #{corUserName},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
            <if test="transLineName != null  and transLineName != ''  ">trans_line_name = #{transLineName},</if>
            <if test="groupName != null  and groupName != ''  ">group_name = #{groupName},</if>
            <if test="dispatcherName != null  and dispatcherName != ''  ">dispatcher_name = #{dispatcherName},</if>
            <if test="custAbbr != null  and custAbbr != ''  ">cust_abbr = #{custAbbr},</if>
            <if test="appDeliContact != null  and appDeliContact != ''  ">app_deli_contact = #{appDeliContact},</if>
            <if test="appDeliMobile != null  and appDeliMobile != ''  ">app_deli_mobile = #{appDeliMobile},</if>
            <if test="srcType != null  and srcType != ''  ">src_type = #{srcType},</if>
            <if test="isClose != null  ">is_close = #{isClose},</if>
            <if test="isRating != null  ">is_rating = #{isRating},</if>
            <if test="isNtocc != null  and isNtocc != ''  ">is_ntocc = #{isNtocc},</if>
            <if test="businesstypename != null  and businesstypename != ''  ">businesstypename = #{businesstypename},
            </if>
        </trim>
        where invoice_id = #{invoiceId}
    </update>

    <!-- 新增货品 -->
    <insert id="insertOrderGoods" parameterType="OrderGoods">
        insert into t_inv_pack_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invPackGoodsId != null  and invPackGoodsId != ''  ">inv_pack_goods_id,</if>
            <if test="delFlag != null  ">del_flag,</if>
            <if test="delDate != null  ">del_date,</if>
            <if test="invoiceId != null  and invoiceId != ''  ">invoice_id,</if>
            <if test="goodsType != null  and goodsType != ''  ">goods_type,</if>
            <if test="goodsId != null  and goodsId != ''  ">goods_id,</if>
            <if test="goodsCode != null  and goodsCode != ''  ">goods_code,</if>
            <if test="goodsName != null  and goodsName != ''  ">goods_name,</if>
            <if test="billingMethod != null  and billingMethod != ''  ">billing_method,</if>
            <if test="num != null  ">num,</if>
            <if test="weight != null  ">weight,</if>
            <if test="volume != null  ">volume,</if>
            <if test="pc != null  ">pc,</if>
            <if test="sum != null  ">sum,</if>
            <if test="packId != null  and packId != ''  ">pack_id,</if>
            <if test="length != null  ">length,</if>
            <if test="width != null  ">width,</if>
            <if test="height != null  ">height,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id,</if>
            <if test="corDate != null  ">cor_date,</if>
            <if test="goodsTypeName != null  and goodsTypeName != ''  ">goods_type_name,</if>
            <if test="goodsCharacter != null  ">goods_character,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="custOrderno != null  and custOrderno != ''  ">cust_orderno,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invPackGoodsId != null  and invPackGoodsId != ''  ">#{invPackGoodsId},</if>
            <if test="delFlag != null  ">#{delFlag},</if>
            <if test="delDate != null  ">#{delDate},</if>
            <if test="invoiceId != null  and invoiceId != ''  ">#{invoiceId},</if>
            <if test="goodsType != null  and goodsType != ''  ">#{goodsType},</if>
            <if test="goodsId != null  and goodsId != ''  ">#{goodsId},</if>
            <if test="goodsCode != null  and goodsCode != ''  ">#{goodsCode},</if>
            <if test="goodsName != null  and goodsName != ''  ">#{goodsName},</if>
            <if test="billingMethod != null  and billingMethod != ''  ">#{billingMethod},</if>
            <if test="num != null  ">#{num},</if>
            <if test="weight != null  ">#{weight},</if>
            <if test="volume != null  ">#{volume},</if>
            <if test="pc != null  ">#{pc},</if>
            <if test="sum != null  ">#{sum},</if>
            <if test="packId != null  and packId != ''  ">#{packId},</if>
            <if test="length != null  ">#{length},</if>
            <if test="width != null  ">#{width},</if>
            <if test="height != null  ">#{height},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">#{corUserId},</if>
            <if test="corDate != null  ">#{corDate},</if>
            <if test="goodsTypeName != null  and goodsTypeName != ''  ">#{goodsTypeName},</if>
            <if test="goodsCharacter != null  ">#{goodsCharacter},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="custOrderno != null  and custOrderno != ''  ">#{custOrderno},</if>
        </trim>
    </insert>

    <!-- 查询货品 -->
    <select id="selectOrderGoods" parameterType="String" resultMap="OrderGoodsResult">
        <include refid="selectOrderGoodsVo"/>
        where invoice_id = #{orderId}
        and del_flag=0
    </select>

    <!-- 删除货品 -->
    <delete id="deleteOrderGoodsById" parameterType="String">
        delete from t_inv_pack_goods where invoice_id =#{invoiceId}
    </delete>

    <!-- 查询客户信息 -->
    <select id="selectCustById" parameterType="Long" resultMap="ClientVOResult">
       select m_customer.customer_id,
           m_customer.cust_name,
           m_customer.bala_corp,
           m_customer.cust_abbr,
           m_group.group_id,
           m_group.group_name,
           m_customer.sales_dept,
           m_customer.bala_dept,
           m_customer.station_dept,
           m_customer.psndoc,
           m_customer.cust_code,
           m_customer.BILLING_CORP,
           m_customer.APP_DELI_CONTACT,
           m_customer.APP_DELI_MOBILE
        from m_customer
      left join m_cust_user
        on m_cust_user.customer_id = m_customer.customer_id
      left join sys_user on sys_user.user_id = m_cust_user.user_id
      left join m_group_cust
        on m_group_cust.customer_id = m_customer.customer_id
      left join m_group
        on m_group_cust.group_id = m_group.group_id
     where sys_user.user_id = #{userId} and m_customer.del_flag = 0
    </select>

    <!-- 获取 Sequence  Invoice -->
    <select id="getSeqInvoice" resultType="int">
        select SEQ_INVOICE.nextval from dual
    </select>

    <!-- 查询异常信息 -->
    <select id="selectExceptionByOrderId" parameterType="String" resultType="map">
        select t_entrust_exp.entrust_exp_id,
               t_entrust_exp.entrust_id,
               t_entrust_exp.exp_type,
               t_entrust_exp.tracking_status,
               t_entrust_exp.exp_occ_time,
               t_entrust_exp.est_arrival_time,
               t_entrust_exp.appendix_id,
               t_entrust_exp.note,
               t_entrust_exp.carrier_id,
               t_entrust_exp.carno_id,
               t_entrust_exp.driver_name,
               t_entrust_exp.driver_mobile,
               t_entrust_exp.reg_user_id,
               t_entrust_exp.reg_date,
               t_entrust_exp.cor_user_id,
               t_entrust_exp.cor_date
          from t_entrust_exp
          left join t_entrust
            on t_entrust.entrust_id = t_entrust_exp.entrust_id
          left join t_invoice
            on t_invoice.invoice_id = t_entrust.orderno
            where t_invoice.invoice_id = #{invoiceId}
            and t_entrust_exp.if_visible_to_cust='1'
            and t_entrust_exp.del_flag=0
    </select>

    <!-- 查询异常图片 -->
    <select id="selectExpPicByExpId" parameterType="String" resultType="map">
        select sys_upload_file.file_path,sys_upload_file.file_name from sys_upload_file
        left join t_entrust_exp on t_entrust_exp.appendix_id=sys_upload_file.tid
        where t_entrust_exp.entrust_exp_id= #{entrustExpId} and sys_upload_file.del_flag = 0
    </select>

    <!-- 删除异常信息 -->
    <select id="selectExpByExpId" parameterType="String" resultType="map">
        <include refid="selectEntrustExpVo"/>
        where entrust_exp_id = #{entrustExpId} and del_flag = 0
    </select>

    <!-- 新增评价详情 -->
    <insert id="insertCustEvaluateDtl" parameterType="CustEvaluateDtl">
        insert into t_cust_evaluate_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="custEvaluateDtlId != null  and custEvaluateDtlId != ''  ">cust_evaluate_dtl_id,</if>
            <if test="custEvaluateId != null  and custEvaluateId != ''  ">cust_evaluate_id,</if>
            <if test="evaluateType != null  and evaluateType != ''  ">evaluate_type,</if>
            <if test="dispatcherGrade != null  ">dispatcher_grade,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regUserId != null  and regUserId != ''  ">cor_user_id,</if>
            <if test="regDate != null  ">cor_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="custEvaluateDtlId != null  and custEvaluateDtlId != ''  ">#{custEvaluateDtlId},</if>
            <if test="custEvaluateId != null  and custEvaluateId != ''  ">#{custEvaluateId},</if>
            <if test="evaluateType != null  and evaluateType != ''  ">#{evaluateType},</if>
            <if test="dispatcherGrade != null  ">#{dispatcherGrade},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
        </trim>
    </insert>

    <!-- 新增评价 -->
    <insert id="insertCustEvaluate" parameterType="CustEvaluate">
        insert into t_cust_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="custEvaluateId != null  and custEvaluateId != ''  ">cust_evaluate_id,</if>
            <if test="memo != null  and memo != ''  ">memo,</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id,</if>
            <if test="regDate != null  ">reg_date,</if>
            <if test="regUserId != null  and regUserId != ''  ">cor_user_id,</if>
            <if test="regDate != null  ">cor_date,</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id,</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id,</if>
            <if test="invoiceId != null  and invoiceId != ''  ">invoice_id,</if>
            <if test="invoiceNo != null  and invoiceNo != ''  ">invoice_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="custEvaluateId != null  and custEvaluateId != ''  ">#{custEvaluateId},</if>
            <if test="memo != null  and memo != ''  ">#{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regUserId != null  and regUserId != ''  ">#{regUserId},</if>
            <if test="regDate != null  ">#{regDate},</if>
            <if test="regScrId != null  and regScrId != ''  ">#{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">#{corScrId},</if>
            <if test="invoiceId != null  and invoiceId != ''  ">#{invoiceId},</if>
            <if test="invoiceNo != null  and invoiceNo != ''  ">#{invoiceNo},</if>
        </trim>
    </insert>

    <!-- 查询评价类型 -->
    <select id="selectDictInfo" resultType="map">
     select dict_type,dict_value,dict_label
      from sys_dict_data
     where dict_type = 'evaluate_type'
    </select>

    <!-- 查询评价详情 -->
    <select id="selectRatingInfoById" parameterType="CustEvaluateDtl" resultMap="CustEvaluateDtlResult">
        <include refid="selectCustEvaluateDtlVo"/>
        where cust_evaluate_id = #{custEvaluateId}
    </select>

    <!-- 查询评价主表 -->
    <select id="selectEvaluate" parameterType="CustEvaluate" resultMap="CustEvaluateResult">
        <include refid="selectCustEvaluateVo"/>
        where invoice_id = #{orderId}
    </select>

    <!-- 根据发货单id数组，查询不为某个状态下的发货单list-->
    <select id="getNoNewListByIds" resultMap="OrderResult">
        <include refid="selectOrderVo"/>
        where INVOICE_ID IN
        <foreach collection="ids" item="invoiceId" open="(" separator="," close=")">
            #{invoiceId}
        </foreach>
        and del_flag = 0
        and vbillstatus != #{status}
    </select>

    <!-- 根据发货单id 更新数据-->
    <update id="updateOrderGoodsByOrderId">
        update t_inv_pack_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="delDate != null  ">del_date = #{delDate},</if>
            <if test="invoiceId != null  and invoiceId != ''  ">invoice_id = #{invoiceId},</if>
            <if test="goodsType != null  and goodsType != ''  ">goods_type = #{goodsType},</if>
            <if test="goodsId != null  and goodsId != ''  ">goods_id = #{goodsId},</if>
            <if test="goodsCode != null  and goodsCode != ''  ">goods_code = #{goodsCode},</if>
            <if test="goodsName != null  and goodsName != ''  ">goods_name = #{goodsName},</if>
            <if test="billingMethod != null  and billingMethod != ''  ">billing_method = #{billingMethod},</if>
            <if test="num != null  ">num = #{num},</if>
            <if test="weight != null  ">weight = #{weight},</if>
            <if test="volume != null  ">volume = #{volume},</if>
            <if test="pc != null  ">pc = #{pc},</if>
            <if test="sum != null  ">sum = #{sum},</if>
            <if test="packId != null  and packId != ''  ">pack_id = #{packId},</if>
            <if test="length != null  ">length = #{length},</if>
            <if test="width != null  ">width = #{width},</if>
            <if test="height != null  ">height = #{height},</if>
            <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
            <if test="regUserId != null  and regUserId != ''  ">reg_user_id = #{regUserId},</if>
            <if test="regDate != null  ">reg_date = #{regDate},</if>
            <if test="corUserId != null  and corUserId != ''  ">cor_user_id = #{corUserId},</if>
            <if test="corDate != null  ">cor_date = #{corDate},</if>
            <if test="goodsTypeName != null  and goodsTypeName != ''  ">goods_type_name = #{goodsTypeName},</if>
            <if test="goodsCharacter != null  ">goods_character = #{goodsCharacter},</if>
            <if test="regScrId != null  and regScrId != ''  ">reg_scr_id = #{regScrId},</if>
            <if test="corScrId != null  and corScrId != ''  ">cor_scr_id = #{corScrId},</if>
        </trim>
        where invoice_id = #{invoiceId}
        and del_flag = 0
    </update>
    <!-- 批量逻辑删除，更新 del_flag 为 1 -->
    <update id="deleteOrdersByIdsLogic">
        UPDATE T_INVOICE SET DEL_FLAG = 1 , DEL_USERID = #{delUserid} , DEL_DATE = SYSDATE
        WHERE INVOICE_ID IN
        <foreach item="invoiceId" collection="ids" open="(" separator="," close=")">
            #{invoiceId}
        </foreach>
        <if test="checkStatus != null and checkStatus != '' ">and vbillstatus = #{checkStatus}</if>

    </update>

    <!-- 根据发货单id数组，查询不为某个状态下的发货单list-->
    <select id="selectOrderGoodsList" parameterType="Client" resultMap="OrderGoodsResult">
        select T_INV_PACK_GOODS.inv_pack_goods_id,
               T_INV_PACK_GOODS.invoice_id,
               T_INV_PACK_GOODS.goods_type,
               T_INV_PACK_GOODS.goods_id,
               T_INV_PACK_GOODS.goods_code,
               T_INV_PACK_GOODS.goods_name,
               T_INV_PACK_GOODS.billing_method,
               T_INV_PACK_GOODS.num,
               T_INV_PACK_GOODS.weight,
               T_INV_PACK_GOODS.volume,
               T_INV_PACK_GOODS.pc,
               T_INV_PACK_GOODS.sum,
               T_INV_PACK_GOODS.pack_id,
               T_INV_PACK_GOODS.length,
               T_INV_PACK_GOODS.width,
               T_INV_PACK_GOODS.height,
               T_INV_PACK_GOODS.memo,
               t_invoice.cust_orderno,
               t_invoice.vbillno,
               t_invoice.req_deli_date,
               t_invoice.req_arri_date,
                t_invoice.deli_addr_name,
               t_invoice.arri_addr_name,
               t_invoice.bala_name,
               t_invoice.cust_name,
               t_invoice.cust_code,
               t_invoice.urgent_level
          from t_inv_pack_goods
          left join t_invoice
            on t_invoice.invoice_id = T_INV_PACK_GOODS.invoice_id
          where customer_id =#{customerId}
        and t_invoice.del_flag = 0
    </select>

    <!--货主入口 发货单 更新评价状态-->
    <update id="updateOrderIsRating" parameterType="Order" >
        update t_invoice set is_rating = 1 , cor_scr_id = #{corScrId} , cor_date = #{corDate},cor_user_name = #{corUserName}
        where invoice_id = #{invoiceId} and is_rating = 0  and del_flag = 0
    </update>

    <select id="selectEvaluateTotal" resultType="EvaluateTotalVO">
        select
          invoice.vbillno,
          evaluate.memo,
          invoice.cust_name custName,
          invoice.req_deli_date reqDeliDate,
          invoice.req_arri_date reqArriDate,
          dtlsum.loisticsScore,
          dtlsum.receiptScore,
          dtlsum.goodsScore,
          dtlsum.serviceScore,
          dtlsum.fulfillScore
        from t_cust_evaluate evaluate
        left join t_invoice invoice
              on invoice.invoice_id = evaluate.invoice_id
        left join
          (select
            dtl.cust_evaluate_id,
            nvl(sum(dtl.dispatcher_grade),0),
            nvl(sum(case when dtl.evaluate_type = '0' then dtl.dispatcher_grade end), 0) loisticsScore,
            nvl(sum(case when dtl.evaluate_type = '1' then dtl.dispatcher_grade end), 0) receiptScore,
            nvl(sum(case when dtl.evaluate_type = '2' then dtl.dispatcher_grade end), 0) goodsScore,
            nvl(sum(case when dtl.evaluate_type = '3' then dtl.dispatcher_grade end), 0) serviceScore,
            nvl(sum(case when dtl.evaluate_type = '4' then dtl.dispatcher_grade end), 0) fulfillScore
           from t_cust_evaluate_dtl dtl group by dtl.cust_evaluate_id
            )dtlsum
            on dtlsum.cust_evaluate_id = evaluate.cust_evaluate_id
            <where>
                <if test="vbillno !=null and vbillno != ''">
                    <bind name="vbillno" value="vbillno + '%'"/>
                    invoice.vbillno like #{vbillno}
                </if>
                <if test="custName !=null and custName != ''">
                    <bind name="custName" value="custName + '%'"/>
                    invoice.cust_name like #{custName}
                </if>
            </where>

    </select>

    <resultMap id="actDeliArriReceiptTime" type="Map">
        <id column="order_id" property="orderId" />
        <result column="RECEIPT_DATE" property="receiptDate" />
        <result column="act_deli_date" property="actDeliDate" />
        <result column="act_arri_date" property="actArriDate" />
    </resultMap>

    <select id="getActDeliArriReceiptTime" parameterType="String" resultMap="actDeliArriReceiptTime">
        select #{orderId} order_id, x.RECEIPT_DATE, y.act_deli_date, z.act_arri_date
        from (
                 select to_char(max(t.RECEIPT_DATE),'YYYY-MM-DD HH24:MI:SS') RECEIPT_DATE
                 from t_entrust t
                 where t.orderno = #{orderId} and t.del_flag = 0
             ) x
                 left join (
            select to_char(min(act_leave_date),'YYYY-MM-DD HH24:MI:SS') act_deli_date
            from t_entrust_work a,
                 t_entrust b
            where a.entrust_id = b.entrust_id
              and a.work_type = '1'
              and b.orderno = #{orderId} and a.del_flag = 0 and b.del_flag = 0
        ) y on 1 = 1
                 left join (
            select to_char(max(act_leave_date),'YYYY-MM-DD HH24:MI:SS') act_arri_date
            from t_entrust_work a,
                 t_entrust b
            where a.entrust_id = b.entrust_id
              and a.work_type = '2'
              and b.orderno = #{orderId} and a.del_flag = 0 and b.del_flag = 0
        ) z on 1 = 1
    </select>

    <select id="listReceiptPicList" parameterType="String" resultType="String">
        select d.file_path
        from t_entrust a
                 left join t_entrust_work b on a.entrust_id = b.entrust_id
                 left join t_entrust_work_detail c
                           on b.entrust_id = c.entrust_id and c.del_flag = 0 and c.work_appendix_type = '3'
                 left join sys_upload_file d on c.APPENDIX_ID = d.tid
        where a.orderno = #{orderId}
          and b.work_type = '2'
          and a.DEL_FLAG = 0
        order by c.reg_date
    </select>

    <select id="listPickUpPicList" resultType="java.lang.String">
        select d.file_path
        from t_entrust a
                 left join t_entrust_work b on a.entrust_id = b.entrust_id
                 left join t_entrust_work_detail c
                           on b.entrust_id = c.entrust_id and c.del_flag = 0
                 left join sys_upload_file d on c.APPENDIX_ID = d.tid
        where a.orderno = #{orderId}
          and b.work_type = '1'
          and a.DEL_FLAG = 0
        order by c.reg_date
    </select>

    <select id="listArrivalPicList" resultType="java.lang.String">
        select d.file_path
        from t_entrust a
                 left join t_entrust_work b on a.entrust_id = b.entrust_id
                 left join t_entrust_work_detail c
                           on b.entrust_id = c.entrust_id and c.del_flag = 0 and c.work_appendix_type != '3'
                 left join sys_upload_file d on c.APPENDIX_ID = d.tid
        where a.orderno = #{orderId}
          and b.work_type = '2'
          and a.DEL_FLAG = 0
        order by c.reg_date
    </select>

    <select id="listPickUpPicListByEntrustId" resultType="java.lang.String">
        select d.file_path
        from t_entrust a
                 left join t_entrust_work b on a.entrust_id = b.entrust_id
                 left join t_entrust_work_detail c
                           on b.entrust_id = c.entrust_id and c.del_flag = 0
                 left join sys_upload_file d on c.APPENDIX_ID = d.tid
        where a.ENTRUST_ID = #{entrustId}
          and b.work_type = '1'
          and a.DEL_FLAG = 0
        order by c.reg_date

    </select>

    <select id="listArrivalPicListByEntrustId" resultType="java.lang.String">
        select d.file_path
        from t_entrust a
                 left join t_entrust_work b on a.entrust_id = b.entrust_id
                 left join t_entrust_work_detail c
                           on b.entrust_id = c.entrust_id and c.del_flag = 0 and c.work_appendix_type != '3'
                 left join sys_upload_file d on c.APPENDIX_ID = d.tid
        where a.ENTRUST_ID = #{entrustId}
          and b.work_type = '2'
          and a.DEL_FLAG = 0
        order by c.reg_date

    </select>
</mapper>