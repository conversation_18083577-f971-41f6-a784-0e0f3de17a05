package com.ruoyi.tms.controller.owner;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.tms.constant.InvoiceStatusEnum;
import com.ruoyi.tms.constant.mobile.MobileUserType;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.Carrierinfo;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.client.Client;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.owner.Order;
import com.ruoyi.tms.domain.owner.OrderGoods;
import com.ruoyi.tms.domain.trace.CarLocus;
import com.ruoyi.tms.domain.trace.EntPackGoods;
import com.ruoyi.tms.domain.trace.EntrustWork;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.trace.EntPackGoodsMapper;
import com.ruoyi.tms.mapper.trace.EntrustWorkMapper;
import com.ruoyi.tms.service.basic.ICarrierService;
import com.ruoyi.tms.service.client.ClientService;
import com.ruoyi.tms.service.client.CustAddressService;
import com.ruoyi.tms.service.finance.IReceiveDetailService;
import com.ruoyi.tms.service.invoice.IInvoiceService;
import com.ruoyi.tms.service.owner.IOrderService;
import com.ruoyi.tms.service.trace.ICarLocusService;
import com.ruoyi.tms.service.trace.ITraceService;
import com.ruoyi.tms.vo.client.ClientVO;
import com.ruoyi.tms.vo.finance.ReceiveDetailVO;
import com.ruoyi.tms.vo.invoice.OperationHistoryVO;
import com.ruoyi.tms.vo.owner.AddressVO;
import com.ruoyi.tms.vo.owner.CustomerBillVO;
import com.ruoyi.tms.vo.trace.EntrustDto;
import com.ruoyi.tms.vo.trace.EntrustVO;
import com.ruoyi.util.ShiroUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 货主入口-订单列表
 */
@Controller
@RequestMapping("/owner/order")
public class OrderController extends BaseController {
    private static final String prefix = "tms/owner/order";
    @Autowired
    private IOrderService orderService;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IInvoiceService invoiceService;
    @Autowired
    private CustAddressService custAddressService;
    @Autowired
    private IReceiveDetailService receiveDetailService;
    @Autowired
    private EntrustMapper entrustMapper;
    @Autowired
    private EntrustWorkMapper entrustWorkMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ICarLocusService carLocusService;
    @Autowired
    private PayDetailMapper payDetailMapper;
    @Autowired
    private EntPackGoodsMapper entPackGoodsMapper;
    @Autowired
    private ICarrierService carrierService;
    @Autowired
    private ClientService clientService;


    @RequiresPermissions("owner:order:view")
    @GetMapping()
    public String order(ModelMap mmap) {
        SysUser user = userService.selectUserById(shiroUtils.getUserId());
        if (user.getUserType() != null) {
            if (MobileUserType.COMPANY.getValue() == Integer.valueOf(user.getUserType())) {
                ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
                mmap.put("client", client);
            }
        }
        //发货单状态List
        mmap.put("orderStatusList", InvoiceStatusEnum.getAllToMap());
        //发货单状态map
        Map<String, Object> value = EnumUtil.getNameFieldMap(InvoiceStatusEnum.class, "value");
        mmap.put("invoiceStatusMap", value);
        return "tms/owner/order/order";
    }

    /**
     * 查询发货单列表
     *
     * @param order 发货单
     * @return
     */
    @RequiresPermissions("owner:order:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Order order, String reqDeliDateRangeStr, String reqArriDateRangeStr) {
        if (StringUtils.isNotEmpty(reqDeliDateRangeStr)) {
            order.setReqDeliDateRange(reqDeliDateRangeStr.split(" - "));
        }
        if (StringUtils.isNotEmpty(reqArriDateRangeStr)) {
            order.setReqArriDateRange(reqArriDateRangeStr.split(" - "));
        }

        List<Order> list = new ArrayList<>();
        SysUser user = userService.selectUserById(shiroUtils.getUserId());
        if (user.getUserType() != null) {
            if (MobileUserType.COMPANY.getValue() == Integer.parseInt(user.getUserType())) {
                ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
                order.setCustomerId(client.getCustomerId());
                startPage();
                list = orderService.selectOrderList(order);
            }
        }
        return getDataTable(list);
    }

    /**
     * 导出发货单列表
     *
     * @param
     * @return
     */
    @RequiresPermissions("owner:order:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export() {
        ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
        List<OrderGoods> list = orderService.selectOrderGoodsList(client);
        ExcelUtil<OrderGoods> util = new ExcelUtil<>(OrderGoods.class);
        return util.exportExcel(list, "发货单信息");
    }

    /**
     * 跳转新增发货单页面
     *
     * @return
     */
    @RequiresPermissions("owner:order:add")
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
        Client custBala = clientService.getDefaultCustBalaByCustomerId(client.getCustomerId());

        Map<String,Object> addressMap=custAddressService.selectDefaultAddress(client.getCustomerId());
        mmap.put("client", client);
        mmap.put("custBala", custBala);
        mmap.put("deliAddr", addressMap.get("deliAddr"));
        mmap.put("arriAddr", addressMap.get("arriAddr"));
        return "tms/owner/order/add";
    }

    /**
     * 新增保存发货单
     *
     * @param order 发货单
     * @return
     */
    @RequiresPermissions("owner:order:add")
    @Log(title = "发货单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated Order order) {
        return orderService.insertOrder(order);
    }

    /**
     * 修改发货单
     *
     * @param orderId 发货单id
     * @param mmap
     * @return
     */
    @RequiresPermissions("owner:order:edit")
    @GetMapping("/edit/{orderId}")
    public String edit(@PathVariable("orderId") String orderId, ModelMap mmap) {
        int flag = 0;
        Map<String, Object> map = orderService.selectOrderById(orderId, flag);
        mmap.put("order", map.get("order"));
        //货物集合
        mmap.put("goodsList", map.get("goodsList"));
        mmap.put("goodsListSize", map.get("goodsListSize"));

        mmap.put("type", "edit");
        return "tms/owner/order/edit";
    }

    /**
     * 修改保存发货单
     *
     * @param order 发货单
     * @return
     */
    @RequiresPermissions("owner:order:edit")
    @Log(title = "发货单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated Order order) {
        Order orderInfo=orderService.selectOrderInfoById(order.getInvoiceId());
        if(order.getCorDate().compareTo(orderInfo.getCorDate())!=0
                || !InvoiceStatusEnum.NEW.getValue().equals(orderInfo.getVbillstatus())){
            return error("该数据已经更新，请关闭该页面刷新列表后重试。");
        }
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 发货单详情
     *
     * @param orderId 发货单id
     * @param mmap
     * @return
     */
    @RequiresPermissions(value = {"owner:order:detail","owner:receiver:detail"},logical = Logical.OR)
    @GetMapping("/detail/{orderId}")
    public String detail(@PathVariable("orderId") String orderId, ModelMap mmap) {
        int flag = 0;
        Map<String, Object> map = orderService.selectOrderById(orderId, flag);
        mmap.put("orderStatusList", InvoiceStatusEnum.getAllToMap());
        mmap.put("order", map.get("order"));
        mmap.put("goodsList", map.get("goodsList"));
        // 实际提货、到货时间，回单时间
        Map<String, Object> act = orderService.getActDeliArriReceiptTime(orderId);
        mmap.putAll(act);
        List<String> picList = orderService.listReceiptPicList(orderId);
        mmap.put("picList", picList);
        List<String> pickUpPicList = orderService.listPickUpPicList(orderId);
        mmap.put("pickUpPicList", pickUpPicList);
        List<String> arrivalPicList = orderService.listArrivalPicList(orderId);
        mmap.put("arrivalPicList", arrivalPicList);

        //发货单信息
        Invoice invoice = invoiceService.selectInvoiceById(orderId);


        ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
        if ((client != null && !invoice.getCustomerId().equals(client.getCustomerId()))
                && (!invoice.getArriUserId().equals(shiroUtils.getUserId())) ) {
            return "error/not_exist";
        }


        mmap.put("invoice", invoice);
        /*
         * 应收
         */
        List<ReceiveDetailVO> receiveDetailVOList = receiveDetailService.selectReceiveByInvoiceId(invoice.getInvoiceId());
        receiveDetailVOList.sort(Comparator.comparing(ReceiveDetailVO::getRegDate));
        mmap.put("receiveDetailList", receiveDetailVOList);

        /*
         *  委托单
         */
        List<OperationHistoryVO> operationHistoryVOList = new ArrayList<>();
        List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());
        for (Entrust entrust : entrustList) {
            if ("0".equals(entrust.getIsFleetData()) && "1".equals(entrust.getIsFleetAssign())) {
                //车队
                Entrust bizSele = new Entrust();
                bizSele.setBizEntrustId(entrust.getEntrustId());
                bizSele.setDelFlag(0);
                List<Entrust> entrustBizList = entrustMapper.selectEntrustList(bizSele);

                for (Entrust bizEntrust : entrustBizList) {
                    OperationHistoryVO operationHistoryVO = new OperationHistoryVO();
                    BeanUtils.copyBeanProp(operationHistoryVO, bizEntrust);

                    EntrustWork entrustWorkSele = new EntrustWork();
                    entrustWorkSele.setEntrustId(bizEntrust.getEntrustId());
                    entrustWorkSele.setDelFlag(0);
                    List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSele);
                    for (EntrustWork entrustWork : entrustWorks) {
                        if ("1".equals(entrustWork.getWorkType())) {
                            SysUser user = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                            //提货
                            operationHistoryVO.setPickUpUserName(user.getUserName());
                            //提货时间
                            operationHistoryVO.setPickUpDate(entrustWork.getRegDate());
                        } else if ("2".equals(entrustWork.getWorkType())) {
                            SysUser user = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                            //到货
                            operationHistoryVO.setArrivalsUserName(user.getUserName());
                            //到货时间
                            operationHistoryVO.setArrivalsDate(entrustWork.getRegDate());
                        }
                    }
                    //应付
                    List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(bizEntrust.getLotId());
                    payDetailList.sort(Comparator.comparing(PayDetail::getRegDate));
                    operationHistoryVO.setPayDetailList(payDetailList);

                    //定位信息
                    List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(bizEntrust.getEntrustId());
                    carLocusList.sort(Comparator.comparing(CarLocus::getTrackingTime));
                    operationHistoryVO.setCarLocusList(carLocusList);

                    operationHistoryVOList.add(operationHistoryVO);
                }
            }else {
                OperationHistoryVO operationHistoryVO = new OperationHistoryVO();
                BeanUtils.copyBeanProp(operationHistoryVO, entrust);

                EntrustWork entrustWorkSele = new EntrustWork();
                entrustWorkSele.setEntrustId(entrust.getEntrustId());
                entrustWorkSele.setDelFlag(0);
                List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSele);
                for (EntrustWork entrustWork : entrustWorks) {
                    if ("1".equals(entrustWork.getWorkType())) {
                        SysUser user = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                        //提货
                        operationHistoryVO.setPickUpUserName(user.getUserName());
                        //提货时间
                        operationHistoryVO.setPickUpDate(entrustWork.getRegDate());
                    } else if ("2".equals(entrustWork.getWorkType())) {
                        SysUser user = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                        //到货
                        operationHistoryVO.setArrivalsUserName(user.getUserName());
                        //到货时间
                        operationHistoryVO.setArrivalsDate(entrustWork.getRegDate());
                    }
                }
                //应付
                List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(entrust.getLotId());
                payDetailList.sort(Comparator.comparing(PayDetail::getRegDate));
                operationHistoryVO.setPayDetailList(payDetailList);

                //定位信息
                List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrust.getEntrustId());
                carLocusList.sort(Comparator.comparing(CarLocus::getTrackingTime));
                operationHistoryVO.setCarLocusList(carLocusList);

                operationHistoryVOList.add(operationHistoryVO);
            }

//            Allocation allocation = new Allocation();
//
//            allocationService.selectAllocationList(allocation);
        }

        mmap.put("operationHistoryVOList", operationHistoryVOList);
        return "tms/owner/order/detail";
    }

    /**
     * 删除发货单
     *
     * @param ids 发货单id
     * @return
     */
    @RequiresPermissions("owner:order:remove")
    @Log(title = "发货单", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return orderService.deleteOrderByIds(ids);
    }

    /**
     * 发货单复制
     *
     * @param orderId 发货单id
     * @param mmap
     * @return
     */
    @RequiresPermissions("owner:order:copy")
    @GetMapping("/copy/{orderId}")
    public String copy(@PathVariable("orderId") String orderId, ModelMap mmap) {
        int flag = 1;
        Map<String, Object> map = orderService.selectOrderById(orderId, flag);
        mmap.put("order", map.get("order"));
        //货物集合
        mmap.put("goodsList", map.get("goodsList"));
        mmap.put("goodsListSize", map.get("goodsListSize"));

        mmap.put("type", "copy");

        return "tms/owner/order/edit";
    }

    /**
     *
     *
     * @param dateType   0按日   1按周   2按月
     * @return
     */
    @PostMapping("/count")
    @RequiresPermissions(value = {"owner:order:list"}, logical = Logical.OR)
    @ResponseBody
    public AjaxResult xslbChats(int dateType) {
        Order order = new Order();

        ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
        order.setCustomerId(client.getCustomerId());

        if (dateType == 0) {
            //当日
            order.setReqDeliDateStart(DateUtil.format(new Date(), "yyyy-MM-dd"));
            order.setReqDeliDateEnd(DateUtil.format(new Date(), "yyyy-MM-dd"));
        } else if (dateType == 1) {
            //当周
            order.setReqDeliDateStart(DateUtil.format(DateUtil.beginOfWeek(new Date()), "yyyy-MM-dd"));
            order.setReqDeliDateEnd(DateUtil.format(DateUtil.endOfWeek(new Date()), "yyyy-MM-dd"));
        }else {
            order.setReqDeliDateStart(DateUtil.format(DateUtil.beginOfMonth(new Date()), "yyyy-MM-dd"));
            order.setReqDeliDateEnd(DateUtil.format(DateUtil.endOfMonth(new Date()), "yyyy-MM-dd"));
        }

        List<Order> list = orderService.selectOrderList(order);

        Map<String, Long> collect = list.stream()
                .peek(x -> {
                    if (("1".equals(x.getVbillstatus()) || "2".equals(x.getVbillstatus())) && x.getSegmentStatus() != 0) {
                        x.setVbillstatus("已派车");
                    } else if ("3".equals(x.getVbillstatus()) || "4".equals(x.getVbillstatus())) {
                        x.setVbillstatus("已提货");
                    } else if ("5".equals(x.getVbillstatus()) || "6".equals(x.getVbillstatus())) {
                        x.setVbillstatus("已到货");
                    } else if ("7".equals(x.getVbillstatus())) {
                        x.setVbillstatus("已回单");
                    }
                })
                .collect(Collectors.groupingBy(Order::getVbillstatus, Collectors.counting()));

        return AjaxResult.success(collect);
    }

    /**
     * 查询发货单异常
     *
     * @param orderId 发货单id
     * @param mmap
     * @return
     */
    @RequiresPermissions("owner:order:exception")
    @GetMapping("/exception/{orderId}")
    public String exception(@PathVariable("orderId") String orderId, ModelMap mmap) {
        List<Map<String, Object>> exceptionList = orderService.selectExceptionByOrderId(orderId);
        mmap.put("exceptionList", exceptionList);
        return prefix + "/exception";
    }

    /**
     * 查询发货单异常图片
     *
     * @param expId 异常id
     * @param mmap
     * @return
     */
    @RequiresPermissions("owner:order:exception")
    @GetMapping("/exceptionDetail/{expId}")
    public String exceptionDetail(@PathVariable("expId") String expId, ModelMap mmap) {
        Map<String, Object> exception = orderService.selectExpByExpId(expId);
        List<Map<String, Object>> expPicList = orderService.selectExpPicByExpId(expId);
        mmap.put("exception", exception);
        mmap.put("expPicList", expPicList);
        return prefix + "/abnormal";
    }

    /**
     * 评价页面
     *
     * @param orderId 发货单id
     * @param mmap
     * @return
     */
    @RequiresPermissions("owner:order:appraise")
    @GetMapping("/appraise")
    public String appraise(@RequestParam String orderId, ModelMap mmap) {
        //根据发货单ID查询已有的评价信息
        Map<String, Object> map = orderService.selectRatingInfoByOrderId(orderId);
        mmap.put("list", map.get("list"));
        mmap.put("evaluate", map.get("evaluate"));
        mmap.put("orderId", orderId);
        mmap.put("orderNo", map.get("orderNo"));
        return prefix + "/appraise";
    }

    /**
     * 保存评价
     *
     * @param params 评价
     * @return
     */
    @RequiresPermissions("owner:order:appraise")
    @Log(title = "发货单", businessType = BusinessType.INSERT)
    @PostMapping("/saveRating")
    @ResponseBody
    public AjaxResult saveRating(@RequestParam Map<String, String> params) {
        try {
            return orderService.saveRating(params);
        }catch (Exception e){
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 下载模板
     *
     * @return
     */
    @RequiresPermissions("owner:order:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<OrderGoods> util = new ExcelUtil<>(OrderGoods.class);
        return util.importTemplateExcel("发货单模板");
    }

    /**
     * 导入
     *
     * @param file 文件
     * @return
     */
 /*   @Log(title = "发货单", businessType = BusinessType.IMPORT)
    @RequiresPermissions("owner:order:import")
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<OrderGoods> util = new ExcelUtil<>(OrderGoods.class);
        List<OrderGoods> goodsList = util.importExcel(file.getInputStream());
        String msg = orderService.importOrder(goodsList);
        return AjaxResult.success(msg);
    }*/

    /**
     * 物流跟踪--公路整车
     * @param orderId
     * @param mmap
     * @return
     */
    @GetMapping("carLocation")
    @RequiresPermissions("owner:order:view")
    public String carLocation(@RequestParam String orderId, ModelMap mmap) {
        Map<String,Object> map=orderService.selectLocation(orderId);
        mmap.put("pointMap", map.get("pointMap"));
        mmap.put("carLocusList",map.get("carLocusList"));
        return prefix + "/carLocation";
    }

    /**
     * 物流跟踪--公路整车
     * @return
     */
    @GetMapping("carLocationOrder")
    public String carLocationOrder(@RequestParam String orderId,ModelMap mmap) {
        List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(orderId);
        if(entrusts != null && entrusts.size() > 0 ) {
            //分配车队单据
            if ("0".equals(entrusts.get(0).getIsFleetData()) && "1".equals(entrusts.get(0).getIsFleetAssign())) {
                //车队
                Entrust bizSele = new Entrust();
                bizSele.setBizEntrustId(entrusts.get(0).getEntrustId());
                bizSele.setDelFlag(0);
                List<Entrust> entrustBizList = entrustMapper.selectEntrustList(bizSele);

                //获取车辆轨迹
                List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrustBizList.get(0).getEntrustId());
                mmap.put("carLocusList", carLocusList);

            }else {
                //获取车辆轨迹
                List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrusts.get(0).getEntrustId());
                mmap.put("carLocusList", carLocusList);
            }



            //获取委托单明细,填写高德地图起始到达地点，城市
//            EntrustDto entrustDto = traceService.selectEntrustWorkById(entrusts.get(0).getEntrustId());

            Invoice invoice = invoiceService.selectInvoiceById(orderId);
            //起点终点标记
            Map<String, String> pointMap = new HashMap<>();
            String deliAddr = invoice.getDeliProName() + invoice.getDeliCityName() + invoice.getDeliAddrName() + invoice.getDeliDetailAddr();
            String arriAddr = invoice.getArriProName() + invoice.getArriCityName() + invoice.getArriAreaName() + invoice.getArriDetailAddr();
            if(deliAddr.contains("上海市市辖区")){
                deliAddr = deliAddr.replace("市辖区","");
            }
            if(arriAddr.contains("北京市市辖区")){
                arriAddr = arriAddr.replace("市辖区","");
            }
            pointMap.put("deliAddr", deliAddr);
            pointMap.put("arriAddr", arriAddr);
            mmap.put("pointMap", pointMap);

        }else{
            mmap.put("carLocusList", null);
        }
        return "tms/trace/carLocation_order";
    }

    /**
     * 物流跟踪--其他
     * @param orderId
     * @return
     */
    @GetMapping("selectPath")
    @RequiresPermissions(value = {"owner:order:view","owner:receiver:view"},logical = Logical.OR)
    public String selectPath(@RequestParam String orderId, ModelMap mmap) {
        List<Map<String,Object>> resList = new ArrayList<>();

        if (!"1".equals(orderId)) {
            Invoice invoice = invoiceService.selectInvoiceById(orderId);

            ClientVO client = orderService.selectCustNameById(shiroUtils.getUserId());
            if (client == null || (!invoice.getCustomerId().equals(client.getCustomerId())
                    && (!invoice.getArriUserId().equals(shiroUtils.getUserId()))) ) {
                return "error/not_exist";
            }

            mmap.put("invoiceVbillno",invoice.getVbillno());
            if(invoice.getVbillno().startsWith("MY")){
                mmap.put("vbillnoStart","铭源");
            }else if(invoice.getVbillno().startsWith("JH")){
                mmap.put("vbillnoStart","吉华");
            }
            //根据发货单查询委托单信息
            List<Entrust> entrusts = entrustMapper.selectEntrustListShowStationByInvoiceId(orderId);
            for(Entrust entrust : entrusts){
                //如果存在分配车队的数据
                if ("0".equals(entrust.getIsFleetData()) && "1".equals(entrust.getIsFleetAssign())) {
                    //车队
                    Entrust bizSele = new Entrust();
                    bizSele.setBizEntrustId(entrust.getEntrustId());
                    bizSele.setDelFlag(0);
                    List<Entrust> entrustBizList = entrustMapper.selectEntrustList(bizSele);

                    for (Entrust entrustBiz : entrustBizList) {
                        Map<String,Object> entrustMap = new HashMap<>();
                        entrustMap.put("entrust",entrustBiz);
                        //跟踪
                        List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrustBiz.getEntrustId());
                        entrustMap.put("carLocusList",carLocusList);
                        //查询回单照片
                        List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrustBiz.getEntrustId());
                        entrustMap.put("sysUploadFiles",sysUploadFiles);
                        resList.add(entrustMap);

                        //查询货品信息
                        String[] entrustIdArr = new String[1];
                        entrustIdArr[0] = entrust.getEntrustId();
                        List<EntPackGoods> entPackGoodsList = entPackGoodsMapper.selectEntPackGoodsListByEntrustIds(entrustIdArr);
                        String goodsName = "";
                        for(EntPackGoods entPackGoods : entPackGoodsList){
                            goodsName = goodsName + entPackGoods.getGoodsName() + ",";
                        }
                        entrustMap.put("goodsName",goodsName.substring(0,goodsName.length()-1));
                    }
                }else {
                    Map<String,Object> entrustMap = new HashMap<>();
                    entrustMap.put("entrust",entrust);
                    //跟踪
                    List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrust.getEntrustId());
                    entrustMap.put("carLocusList",carLocusList);
                    //查询回单照片
                    List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());
                    entrustMap.put("sysUploadFiles",sysUploadFiles);
                    resList.add(entrustMap);

                    //查询货品信息
                    String[] entrustIdArr = new String[1];
                    entrustIdArr[0] = entrust.getEntrustId();
                    List<EntPackGoods> entPackGoodsList = entPackGoodsMapper.selectEntPackGoodsListByEntrustIds(entrustIdArr);
                    String goodsName = "";
                    for(EntPackGoods entPackGoods : entPackGoodsList){
                        goodsName = goodsName + entPackGoods.getGoodsName() + ",";
                    }
                    entrustMap.put("goodsName",goodsName.substring(0,goodsName.length()-1));
                }
            }
        }
        mmap.put("resList",resList);
        mmap.put("orderId",orderId);

        return "tms/owner/order/path";
    }


    /**
     * 新增承运商
     * @param mmap
     * @return
     */
    @RequestMapping("/addCarrier")
    public String addCarrier(ModelMap mmap) {
        return "tms/owner/order/add_carrier";
    }


    /**
     * 保存承运商
     *
     * @return
     */
    @RequestMapping("/saveCarrier")
    @ResponseBody
    public AjaxResult saveCarrier(Carrier carrier) {
        //手机号验证
        SysUser sysUser = new SysUser();
        sysUser.setUserType("2");
        sysUser.setPhonenumber(carrier.getPhone());
        if( UserConstants.USER_PHONE_NOT_UNIQUE.equals(sysUserService.checkPhoneUnique(sysUser))){
            return AjaxResult.error("手机号码已存在");
        }
        return orderService.saveCarrier(carrier);
    }


    /**
     * 承运商管理
     * @param mmap
     * @return
     */
    @RequestMapping("/carrier")
    public String carrier(ModelMap mmap) {
        return "tms/owner/order/carrier";
    }

    /**
     * 查询承运商列表
     *
     * @param carrier
     * @return
     */
    @PostMapping("/carrierList")
    @ResponseBody
    public TableDataInfo list(Carrier carrier) {
        startPage();
        carrier.setBlongUserId(shiroUtils.getUserId().toString());
        List<Carrier> list = carrierService.selectCarrierList(carrier);
        return getDataTable(list);
    }

    /**
     * 查询实际承运人
     * @param keyword 实际承运人名称
     * @return
     */
    @GetMapping("/findCarrierInfo")
    @ResponseBody
    public AjaxResult findCarrierInfo(String keyword) {
        //查询实际承运人
        Carrier carrier = new Carrier();
        carrier.setCarrName(keyword);
        carrier.setBlongUserId(shiroUtils.getUserId().toString());
        List<Carrier> list = carrierService.selectCarrierList(carrier);
        //封装结果集
        AjaxResult ajaxResult = new AjaxResult();
        ajaxResult.put("code", 200);
        ajaxResult.put("value", list);
        return ajaxResult;
    }

}


