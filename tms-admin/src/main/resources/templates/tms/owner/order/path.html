<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <title>物流跟踪</title>
    <th:block th:include="include :: header('物流跟踪')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container {
        width: 800px;
        margin: 50px auto;
    }

    .vertical-timeline,
    .vertical-container {
        position: relative;
    }

    .vertical-container {
        width: 100%;
    }

    .vertical-timeline-block {
        /* width: 88%; */
        /* border-left: 2px solid #E6E6E6; */
        width: calc(100% - 8em);
        margin: 0 0 0 8em;
        position: relative;
        /* padding-bottom: 10px; */

        cursor: pointer;
    }

    .vertical-timeline-block:after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 1px;
        background-color: #E6E6E6;
    }

    .vertical-timeline-block:first-child::after {
        top: 20px;
        height: calc(100% - 20px)
    }

    .bBlockB::after {
        height: 34px
    }


    /* 时间轴的左侧图标 */
    .vertical-timeline-icon {
        width: 20px;
        height: 20px;
        border-radius: 10px;
        position: absolute;
        left: -10px;
        top: 14px;
        z-index: 1;

        text-align: center;
        line-height: 20px;
        cursor: pointer;
        transition: all 0.2s ease-in;
        -webkit-transition: all 0.2s ease-in;
        -moz-transition: all 0.2s ease-in;
        -o-transition: all 0.2s ease-in;
        border: none;

    }

    .vertical-timeline-iconT {
        width: 10px;
        height: 10px;
        border-radius: 5px;
        position: absolute;
        left: -5px;
        top: 20px;
        line-height: 10px;
        border: 1px solid #E6E6E6;
    }

    /* .vertical-timeline-block:hover .vertical-timeline-icon {
        width: 20px;
        height: 20px;
        border-radius: 10px;
        line-height: 20px;
        left: -27px;
        box-shadow: 0 0 5px #CCC;
        font-size: 25px;
    } */

    /* 时间轴的左侧图标的各种样式 */
    .v-timeline-icon1 {
        background-color: #2aabd2;
    }

    .v-timeline-icon2 {
        background-color: #5cb85c;
    }

    .v-timeline-icon3 {
        background-color: #8c8c8c;
    }

    /* 时间轴的左侧图标上的序号*/
    .vertical-timeline-icon i {
        font-style: normal;
        color: #FFF;
        /* top: 25%; */
        font-size: 16px;
    }

    /* 时间轴的具体内容的格式 */
    .timeline-content {
        text-indent: 2em;
    }

    .time-more {
        overflow: hidden;
    }

    .time-more .time {
        float: left;
        line-height: 40px;
        display: inline-block;
    }

    .time-more .more {
        float: right;
    }

    .time-more .more a {
        display: inline-block;
        height: 20px;
        padding: 8px 15px;
        color: #FFF;
        text-decoration: none;
        font-size: 15px;
    }

    /* “更多信息”的风格 */
    .more-style1 {
        border-radius: 5px;
        background-color: #565656;
    }

    .more-style1:hover {
        background-color: #696969;
    }

    .more-style2 {
        border-radius: 5px;
        background-color: #1AB394;
    }

    .more-style2:hover {
        background-color: #18A689;
    }

    .more-style3 {
        border-radius: 5px;
        background-color: #1C84C6;
    }

    .more-style3:hover {
        background-color: #1A7BB9;
    }

    .vtb-tit {
        width: 6em;
        height: 40px;
        line-height: 40px;
        text-align: center;

        position: absolute;
        left: -7em;
        top: 4px;
    }

    .vertical-timeline-content {
        margin-left: 25px;
        padding: .5em
    }

    .fc69c {
        color: #1ab394;
    }

    .fced5 {
        color: #ed5565;
    }

    .fc80 {
        color: #808080;
    }

    .fc1a {
        color: #1A1A1A;
    }

    .fcff6 {
        color: #FF6A00
    }

    .fc33 {
        color: #333333
    }

    .mt5 {
        margin-top: 5px;
    }

    .f16 {
        font-size: 16px;
    }

    .f14 {
        font-size: 14px;
    }

    .mask-layer-imgbox {
        background-color: #ffffff;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .panel-body {
        padding: 10px;
        box-sizing: border-box;
        max-height: calc(90vh - 100px);
        overflow-y: auto;
    }

    .iframe-main {
        width: 100%;
        height: calc(100vh - 0px);
    }

    .container-fluid {
        position: relative;
        padding: 0;
    }
    .pofp{
        position: fixed;
        top: 40px;
        right: 20px;
        width: 40%;
    }
    .tlDiv {
       
        max-height: 90vh;
        overflow-y: auto;
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid #b1d8f7;
    }
    .leftIcon{
        position: absolute;
        z-index: 2;
        left: -45px;
        top: calc(50% - 12px);
        background-color: #ffffff;
        /* border-top-left-radius: 4px; */
        /* border-bottom-left-radius: 4px; */
        padding: 6px 0 6px 6px ;
        border-left: 1px solid #b1d8f7;
        cursor:pointer;
    }
    .leftIcon::before{
        content:" ";
        width: 43px;
        height: 24px;
        position: absolute;
        left: 1px;
        top: -19px;
        background-color: #ffffff;
        transform: rotateZ(-45deg);
        border-top: 1px solid #b1d8f7;
        border-bottom-right-radius: 50%;
    }
    .leftIcon::after{
        content:" ";
        width: 43px;
        height: 24px;
        position: absolute;
        left: 1px;
        top: 33px;
        background-color: #ffffff;
        transform: rotateZ(45deg);
        border-bottom: 1px solid #b1d8f7;
        border-top-right-radius: 50%;
    }

    .bDiv {
        padding: 10px;
    }

    .bDiv+.bDiv {
        padding-top: 0;
    }

    .selectOpen{
        transition:  .3s ease-in;
        transform-origin: 50% 0;
    }
    h4{
        margin: 0;
    }
    /* 媒体文件容器样式 */
    .media-container {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }
    .video-thumbnail:hover {
        border-color: #337ab7 !important;
        background: #e8f4fd !important;
    }
    .video-thumbnail:hover i {
        color: #23527c !important;
    }
    .picviewer img:hover {
        opacity: 0.8;
    }
    /* 视频模态框样式 */
    .video-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.8);
    }
    .video-modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
        max-height: 90%;
    }
    .video-modal video {
        width: 100%;
        height: auto;
        max-height: 80vh;
    }
    .video-modal-close {
        position: absolute;
        top: 10px;
        right: 25px;
        color: white;
        font-size: 35px;
        font-weight: bold;
        cursor: pointer;
    }
    .video-modal-close:hover {
        color: #ccc;
    }
</style>

<body style="background-color: #F5F5F5;">
    <div class="container-fluid">
        <div class="row" style="margin:0;" th:if="${invoiceVbillno!=''&&invoiceVbillno!=null}">
            <div class="col-sm-12" style="background-color: #ffffff;padding-bottom: 5px;">
                <h4>发货单号：[[${invoiceVbillno}]]</h4>
            </div>
        </div>

        <div class="iframe-main">
            <iframe th:src="@{'/owner/order/carLocationOrder?orderId='+${orderId}}" id="sunPage" width="100%"
                height="100%" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no"
                allowtransparency="yes"></iframe>
        </div>
        <div class="pofp selectOpen" th:if="${not #lists.isEmpty(resList)}">
            <div class="leftIcon">
                <i class="glyphicon glyphicon-indent-left" style="font-size: 24px;z-index: 3;" onclick="fadeOutLeft(this)"></i>
            </div>
           
            <div class="row tlDiv">
                <div class="col-sm-12 bDiv" th:each="res,stat : ${resList}">
                    <div class="panel-group" th:id="|accordion${stat.index}|">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <a data-toggle="collapse" th:data-parent="|#accordion${stat.index}|"
                                        th:href="|#collapseOne${stat.index}|">
                                        <span class="f14">[[${res.entrust.deliProName}]][[${res.entrust.deliCityName}]] →
                                            [[${res.entrust.arriProName}]][[${res.entrust.arriCityName}]]</span>
                                    </a>
                                    <span class="f14" style="margin-left: 20px">
                                        [[${res.goodsName}]]
                                        <span
                                            th:if="${res.entrust.numCount != null && res.entrust.numCount != 0}">｜[[${res.entrust.numCount}]]件
                                        </span>
                                        <span
                                            th:if="${res.entrust.weightCount != null && res.entrust.weightCount != 0}">｜[[${res.entrust.weightCount}]]吨
                                        </span>
                                        <span
                                            th:if="${res.entrust.volumeCount != null && res.entrust.volumeCount != 0}">｜[[${res.entrust.volumeCount}]]m³
                                        </span>
                                    </span>
                                </h4>
                            </div>
                            <div th:id="|collapseOne${stat.index}|" class="panel-collapse collapse in">
                                <div class="panel-body">


                                    <div class="vertical-container light-timeline">
                                        <div class="vertical-timeline-block" th:if="${res.entrust.ifReceipt == '1'}">
                                            <div class="vtb-tit f14">
                                                [[${#dates.format(res.entrust.receiptDate,'MM-dd HH:mm')}]]
                                            </div>
                                            <div class="vertical-timeline-icon yellow-bg">
                                                <i class="icon">✔</i>
                                            </div>

                                            <div class="vertical-timeline-content">
                                                <div class="fcff6 mt5 f16">[回单签收]</div>
                                                <div class="fc1a mt5 f14">[[${res.entrust.receiptMan}]]进行了回单签收</div>
                                            </div>
                                        </div>

                                        <div class="vertical-timeline-block" th:if="${res.entrust.receiptConfirmFlag == 1}">
                                            <div class="vtb-tit f14">
                                                [[${#dates.format(res.entrust.receiptConfirmTime,'MM-dd HH:mm')}]]
                                            </div>
                                            <div class="vertical-timeline-icon yellow-bg">
                                                <i class="icon">✔</i>
                                            </div>

                                            <div class="vertical-timeline-content">
                                                <div class="fcff6 mt5 f16">[回单确认]</div>
                                                <div class="fc1a mt5 f14">
                                                    <div class="fl">
                                                        [[${res.entrust.receiptConfirmUser}]]进行了回单确认，回单照片：
                                                    </div>
                                                    <div class="fl">
                                                        <div class="media-container">
                                                            <!-- 图片容器 -->
                                                            <div class="picviewer" style="display: inline-block;">
                                                                <span th:each="pic:${res.sysUploadFiles}" th:if="${#strings.toLowerCase(pic.filePath).endsWith('.jpg') or #strings.toLowerCase(pic.filePath).endsWith('.jpeg') or #strings.toLowerCase(pic.filePath).endsWith('.png') or #strings.toLowerCase(pic.filePath).endsWith('.gif') or #strings.toLowerCase(pic.filePath).endsWith('.bmp')}">
                                                                    <img style="height: 34px;display: inline-block;margin: 0 2px;cursor:pointer;" th:src="@{${pic.filePath}}" />
                                                                </span>
                                                            </div>
                                                            <!-- 视频容器 -->
                                                            <div class="video-container" style="display: inline-block;">
                                                                <span th:each="pic:${res.sysUploadFiles}" th:if="${#strings.toLowerCase(pic.filePath).endsWith('.mp4') or #strings.toLowerCase(pic.filePath).endsWith('.avi') or #strings.toLowerCase(pic.filePath).endsWith('.mov') or #strings.toLowerCase(pic.filePath).endsWith('.wmv') or #strings.toLowerCase(pic.filePath).endsWith('.flv') or #strings.toLowerCase(pic.filePath).endsWith('.webm')}">
                                                                    <div class="video-thumbnail" th:data-video="@{${pic.filePath}}" style="height:34px;width:48px;margin:0 2px;cursor:pointer;border:1px solid #ddd;border-radius:4px;background:#f5f5f5;display:inline-flex;align-items:center;justify-content:center;position:relative;">
                                                                        <i class="fa fa-play-circle" style="font-size:14px;color:#337ab7;"></i>
                                                                        <span style="position:absolute;bottom:1px;right:1px;background:rgba(0,0,0,0.7);color:white;font-size:8px;padding:1px 2px;border-radius:2px;">视频</span>
                                                                    </div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="vertical-timeline-block" th:if="${res.entrust.actArriDate != null}">
                                            <div class="vtb-tit f14">
                                                [[${#dates.format(res.entrust.actArriDate,'MM-dd HH:mm')}]]
                                            </div>
                                            <div class="vertical-timeline-icon lazur-bg">
                                                <i class="icon">✔</i>
                                            </div>

                                            <div class="vertical-timeline-content">
                                                <div class="fc80 mt5 f16">[到货]</div>
                                                <div class="fc1a mt5 f14">
                                                    <div class="fl">
                                                        送货到达 [[${res.entrust.arriProName}]][[${res.entrust.arriCityName}]][[${res.entrust.arriAreaName}]][[${res.entrust.arriDetailAddr}]]
                                                    </div>
                                                    <div class="fl" th:if="${res.arrivalPicList != null && #lists.size(res.arrivalPicList) > 0}">
                                                        ，到货照片：
                                                        <div class="media-container" style="display: inline-block;">
                                                            <!-- 图片容器 -->
                                                            <div class="picviewer" style="display: inline-block;">
                                                                <span th:each="pic:${res.arrivalPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.jpg') or #strings.toLowerCase(pic).endsWith('.jpeg') or #strings.toLowerCase(pic).endsWith('.png') or #strings.toLowerCase(pic).endsWith('.gif') or #strings.toLowerCase(pic).endsWith('.bmp')}">
                                                                    <img style="height: 34px;display: inline-block;margin: 0 2px;cursor:pointer;" th:src="@{${pic}}" />
                                                                </span>
                                                            </div>
                                                            <!-- 视频容器 -->
                                                            <div class="video-container" style="display: inline-block;">
                                                                <span th:each="pic:${res.arrivalPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.mp4') or #strings.toLowerCase(pic).endsWith('.avi') or #strings.toLowerCase(pic).endsWith('.mov') or #strings.toLowerCase(pic).endsWith('.wmv') or #strings.toLowerCase(pic).endsWith('.flv') or #strings.toLowerCase(pic).endsWith('.webm')}">
                                                                    <div class="video-thumbnail" th:data-video="@{${pic}}" style="height:34px;width:48px;margin:0 2px;cursor:pointer;border:1px solid #ddd;border-radius:4px;background:#f5f5f5;display:inline-flex;align-items:center;justify-content:center;position:relative;">
                                                                        <i class="fa fa-play-circle" style="font-size:14px;color:#337ab7;"></i>
                                                                        <span style="position:absolute;bottom:1px;right:1px;background:rgba(0,0,0,0.7);color:white;font-size:8px;padding:1px 2px;border-radius:2px;">视频</span>
                                                                    </div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div th:if="${res.carLocusList != null}"
                                            th:each="carLocus,statT:${res.carLocusList}">
                                            <div class="vertical-timeline-block">
                                                <div class="vtb-tit f14">
                                                    [[${#dates.format(carLocus.getlocationtime,'MM-dd HH:mm')}]]
                                                </div>
                                                <div class="vertical-timeline-icon white-bg vertical-timeline-iconT"></div>

                                                <div class="vertical-timeline-content">
                                                    <div class="fc33 mt5 f16">[途径]</div>
                                                    <div class="fc1a mt5 f14">
                                                        [[${carLocus.proName}]][[${carLocus.cityName}]][[${carLocus.areaName}]][[${carLocus.detailAddr}]]
                                                        <span
                                                            th:if="${(statT.index==0) && (res.entrust.actArriDate==null) && (res.entrust.receiptConfirmTime==null) && (res.entrust.receiptDate==null)}">
                                                            <button type="button" class="btn btn-primary btn-sm"
                                                                th:onclick="onPosition([[${carLocus.vehiclenumber}]])">当前定位</button>
                                                        </span>
                                                    </div>


                                                </div>
                                            </div>
                                        </div>

                                        <div class="vertical-timeline-block" th:if="${res.entrust.actDeliDate != null}">
                                            <div class="vtb-tit f14">
                                                [[${#dates.format(res.entrust.actDeliDate,'MM-dd HH:mm')}]]
                                            </div>
                                            <div class="vertical-timeline-icon white-bg vertical-timeline-iconT"></div>

                                            <div class="vertical-timeline-content">
                                                <div class="fc33 mt5 f16">[装货]</div>
                                                <div class="fc1a mt5 f14">
                                                    <div class="fl">
                                                        司机在 [[${res.entrust.deliProName}]][[${res.entrust.deliCityName}]][[${res.entrust.deliAreaName}]][[${res.entrust.deliDetailAddr}]] 装货
                                                    </div>
                                                    <div class="fl" th:if="${res.pickUpPicList != null && #lists.size(res.pickUpPicList) > 0}">
                                                        提货照片：
                                                        <div class="media-container" style="display: inline-block;">
                                                            <!-- 图片容器 -->
                                                            <div class="picviewer" style="display: inline-block;">
                                                                <span th:each="pic:${res.pickUpPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.jpg') or #strings.toLowerCase(pic).endsWith('.jpeg') or #strings.toLowerCase(pic).endsWith('.png') or #strings.toLowerCase(pic).endsWith('.gif') or #strings.toLowerCase(pic).endsWith('.bmp')}">
                                                                    <img style="height: 34px;display: inline-block;margin: 0 2px;cursor:pointer;" th:src="@{${pic}}" />
                                                                </span>
                                                            </div>
                                                            <!-- 视频容器 -->
                                                            <div class="video-container" style="display: inline-block;">
                                                                <span th:each="pic:${res.pickUpPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.mp4') or #strings.toLowerCase(pic).endsWith('.avi') or #strings.toLowerCase(pic).endsWith('.mov') or #strings.toLowerCase(pic).endsWith('.wmv') or #strings.toLowerCase(pic).endsWith('.flv') or #strings.toLowerCase(pic).endsWith('.webm')}">
                                                                    <div class="video-thumbnail" th:data-video="@{${pic}}" style="height:34px;width:48px;margin:0 2px;cursor:pointer;border:1px solid #ddd;border-radius:4px;background:#f5f5f5;display:inline-flex;align-items:center;justify-content:center;position:relative;">
                                                                        <i class="fa fa-play-circle" style="font-size:14px;color:#337ab7;"></i>
                                                                        <span style="position:absolute;bottom:1px;right:1px;background:rgba(0,0,0,0.7);color:white;font-size:8px;padding:1px 2px;border-radius:2px;">视频</span>
                                                                    </div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="vertical-timeline-block bBlockB">
                                            <div class="vtb-tit f14">
                                                [[${#dates.format(res.entrust.regDate,'MM-dd HH:mm')}]]
                                            </div>
                                            <div class="vertical-timeline-icon white-bg ">
                                                <img th:src="@{/img/pc.png}"
                                                    style="width: 100%;display: block;margin: 0 auto">
                                            </div>

                                            <div class="vertical-timeline-content">
                                                <div class="fc33 mt5 f16">[派车]</div>
                                                <div class="fc1a mt5 f14">[[${res.entrust.carno}]] 为您服务</div>
                                            </div>
                                        </div>

                                    </div>




                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 视频模态框 -->
    <div id="videoModal" class="video-modal">
        <span class="video-modal-close">&times;</span>
        <div class="video-modal-content">
            <video id="modalVideo" controls>
                您的浏览器不支持视频播放
            </video>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        $(function () {
            // 处理视频缩略图点击事件
            $(document).on('click', '.video-thumbnail', function() {
                var videoSrc = $(this).data('video');
                var modal = $('#videoModal');
                var modalVideo = $('#modalVideo');

                modalVideo.attr('src', videoSrc);
                modal.show();

                // 自动播放视频
                modalVideo[0].play();
            });

            // 关闭视频模态框
            $('.video-modal-close, .video-modal').on('click', function(e) {
                if (e.target === this) {
                    var modal = $('#videoModal');
                    var modalVideo = $('#modalVideo');

                    modalVideo[0].pause();
                    modalVideo.attr('src', '');
                    modal.hide();
                }
            });

            // 阻止视频区域点击事件冒泡
            $('.video-modal-content').on('click', function(e) {
                e.stopPropagation();
            });

            // ESC键关闭模态框
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // ESC键
                    var modal = $('#videoModal');
                    if (modal.is(':visible')) {
                        var modalVideo = $('#modalVideo');
                        modalVideo[0].pause();
                        modalVideo.attr('src', '');
                        modal.hide();
                    }
                }
            });
        });

        function onPosition(carNo) {
            $.ajax({
                url: ctx + "trace/getCarLocation",
                method: 'post',
                dataType: "json",
                data: { carNo },
                success: function (result) {
                    if (result.code == 0) {
                        // $.modal.msgSuccess(result.msg);

                        $("#sunPage")[0].contentWindow.addrMapList(carNo, result.data);
                    } else {
                        $.modal.alertError("暂未获取车辆定位！");
                    }
                }
            });

            // location.reload(true);
        }

        function fadeOutLeft(t) {
            let right=$(t).parent().parent().css("right")
            if(right=="20px"){
                $(t).parent().parent().css( 'right','calc(-40% - 4px)')
                $(t).removeClass("glyphicon-indent-left")
                $(t).addClass("glyphicon-indent-right")
            }else{
                $(t).parent().parent().css( 'right','20px')
                $(t).removeClass("glyphicon-indent-right")
                $(t).addClass("glyphicon-indent-left")
            }
            
        }
    </script>
</body>

</html>