<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单')"/>
</head>
<style>
    .input-group{
        display: block;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;

    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
        color: #808080;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
        /*line-height: 26px;*/
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }

    .timeline{
        /*width: 300px;*/

        position: relative;
    }
    .borgreen{
        border-top: 2px #0ba687 solid;
    }
    .bordark{
        border-top: 2px #bfbfbf solid;
    }
    .timeline_point{
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /*background: #0ba687;*/
        top: -7px;
        left: 50px;
    }

    .tc{
        text-align: center;
    }

    .timeline_vertical {
        /*position: absolute;*/
        /*right: 23px;*/
        /*top: 100px;*/
        padding: 100px 0 0 0;
        margin-left: -3px;
    }
    .timeline_text{
        /*width: 150px;*/
        text-align: center;
        /*border-top-right-radius: 20px;*/
        /*height: 100px;*/
        box-sizing: border-box;
        padding: 20px 0 0 10px;
        line-height: 20px;
    }
    .borrgreen{
        border-right: 3px #0ba687 solid;
    }
    .borrdark{
        border-right: 3px #bfbfbf solid;
    }
    .lazur-bg{
        background: #bfbfbf;
        width: 20px;
        height: 20px;
    }
    .lawarn-bg{
        background: #0ba687;
        width: 20px;
        height: 20px;
    }
    .vertical-container {
        width: 100%;
        max-width: none;
        margin: 0 auto;
    }
    .vertical-timeline-content{
        margin-left: 20px;
    }
    .box_timeline{

    }
    .fl{
        float: left;
    }
    .clear{
        clear: both;
    }
    .fc80{
        color: #808080;
    }
    .fw{
        font-weight: bold;
    }
    .f12{
        font-size: 12px;
    }
    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }
    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #0ba687;
    }
    .vertical-timeline-block-dark::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #bfbfbf;
    }
    /*.timeline_vertical-container.light-timeline:before {*/
    /*    background: #0ba687;*/
    /*}*/
    /*.timeline_vertical-container::before {*/
    /*    left: 0px;*/
    /*}*/
    .vertical-timeline-icon {
        left: -8px;
        top: auto;
    }
    .vertical-timeline-block {
        margin: 0 0px;
    }
    .vertical-timeline-block-dark {
        margin: 0 0px;
    }
    /*.col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
        padding-left: 0px;
        padding-right: 0px;
    }*/
    .bggreen{
        background: #0ba687;
    }
    .bgdark{
        background: #bfbfbf;
    }
    .fc1ab{
        color: #1ab394;
    }
    .eclipse{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .over{
        overflow: hidden;
    }
    .mt20{
        margin-top: 20px;
    }
    .f16{
        font-size: 16px;
    }
    .round{
        width: 5px;
        height: 5px;
        border-radius: 50%;
        position: absolute;
        top: 0;
    }
    .bg00a{
        background: #00a65a;
    }
    .bgbf{
        background: #bfbfbf;
    }
    .rour{
        right: -3px;
    }
    .roul{
        left: -3px;
    }
    .dashed{
        padding: 0 20px 20px 0;
        /*border-right: 1px dashed #00a65a;*/
        position: relative;
    }
    .bordash{
        border-right: 1px dashed #00a65a;
    }
    .bordashda{
        border-right: 1px dashed #bfbfbf;
    }
    .bordashle{
        border-left: 1px dashed #00a65a;
    }
    .bordashleda{
        border-left: 1px dashed #bfbfbf;
    }
    .vercontent{
        border-right: 2px solid #00a65a;
        box-sizing: border-box;
        border-top-right-radius: 30px;
        width: 100%;
        height: 270px;
    }
    .vercontent2{
        border-left: 2px solid #00a65a;
        box-sizing: border-box;
        padding: 0px 0 20px 0;
        margin-left: -2px !important;
    }
    .recode .row{
        margin: 0;
    }
    .flowcontent{
        border: 1px #e1e2e3 solid;
        border-top: 0;
        width: 100%;
        height: 250px;
        overflow: auto;
        box-sizing: border-box;
        padding: 0 5px;
    }
    .timeline_point2 {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /* background: #0ba687; */
        top: -7px;
        left: 50%;
        margin-left: -7px;
    }
    .col-sm-4{
        padding-right: 0;
    }
    /* 媒体文件容器样式 */
    .media-container {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }
    .video-thumbnail:hover {
        border-color: #337ab7 !important;
        background: #e8f4fd !important;
    }
    .video-thumbnail:hover i {
        color: #23527c !important;
    }
    .picviewer img:hover {
        opacity: 0.8;
    }
    /* 视频模态框样式 */
    .video-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.8);
    }
    .video-modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
        max-height: 90%;
    }
    .video-modal video {
        width: 100%;
        height: auto;
        max-height: 80vh;
    }
    .video-modal-close {
        position: absolute;
        top: 10px;
        right: 25px;
        color: white;
        font-size: 35px;
        font-weight: bold;
        cursor: pointer;
    }
    .video-modal-close:hover {
        color: #ccc;
    }
</style>
<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">订单基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单基础信息 begin-->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-4">订单号：</label>
                                    <div class="col-sm-8" th:text="${order.vbillno}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-4">要求提货日期：</label>
                                    <div class="col-sm-8" th:text="*{#dates.format(order.reqDeliDate, 'yyyy-MM-dd HH:hh:mm')}"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-4">要求到货日期：</label>
                                    <div class="col-sm-8" th:text="*{#dates.format(order.reqArriDate, 'yyyy-MM-dd HH:hh:mm')}"></div>
                                </div>
                            </div>
                            <!-- <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">结算公司：</label>
                                    <div class="col-sm-8"
                                         th:each="dict : ${@dict.getType('bala_corp')}"
                                         th:if="${dict.dictValue} == ${order.balaCorpId}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div> -->
                        </div>
                        <div class="row">

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-4">车长：</label>
                                    <div class="col-sm-8"
                                         th:each="dict : ${@dict.getType('car_len')}"
                                         th:if="${dict.dictValue} == ${order.carLen}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-4">车型：</label>
                                    <div class="col-sm-8"
                                         th:each="dict : ${@dict.getType('car_type')}"
                                         th:if="${dict.dictValue} == ${order.carType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-4">紧急程度：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('urgent_level')}"
                                         th:if="${dict.dictValue} == ${order.urgentLevel}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">是否开票：</label>
                                    <div class="col-sm-8"
                                         th:each="dict : ${@dict.getType('if_billing')}"
                                         th:if="${dict.dictValue} == ${order.ifBilling}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div> -->
                        </div>

                        <!-- <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">发货单备注：</label>
                                    <div class="col-md-11 col-sm-10" th:utext="*{#strings.unescapeJava(#strings.replace(#strings.escapeJava(order.memo),'\n','&lt;br/&gt;'))}">
                                    </div>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">订单提货信息</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单提货信息 end-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">提货方：</label>
                                    <div class="col-sm-8" th:text="${order.deliAddrName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">联系电话：</label>
                                    <div class="col-sm-8" th:text="${order.deliMobile}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">联系人：</label>
                                    <div class="col-sm-8" th:text="${order.deliContact}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">地址：</label>
                                    <div class="col-sm-11"
                                         th:text="${order.deliProName}+${order.deliCityName}+${order.deliAreaName}+${order.deliDetailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--订单提货信息 end-->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">订单收货信息</a>
                    </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单收货信息 end-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">收货方：</label>
                                    <div class="col-md-8 col-sm-8" th:text="${order.arriAddrName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">联系电话：</label>
                                    <div class="col-sm-8" th:text="${order.arriMobile}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">联系人：</label>
                                    <div class="col-sm-8" th:text="${order.arriContact}">
                                        李四
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">地址：</label>
                                    <div class="col-sm-11"
                                         th:text="${order.arriProName}+${order.arriCityName}+${order.arriAreaName}+${order.arriDetailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--订单收货信息 end-->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">货品明细</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单货品费用明细 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总件数：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group" th:text="|${order.numCount}件|">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总重量：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group" th:text="|${order.weightCount}吨|">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总体积：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group" th:text="|${order.volumeCount}m3|">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 15%;text-align: left">货品名称</th>
                                    <th style="width: 12%;text-align: left">货品类型</th>
                                    <th style="width: 8%;text-align: left">件数</th>
                                    <th style="width: 8%;text-align: left">重量</th>
                                    <th style="width: 8%;text-align: left">体积</th>
                                    <th style="width: 8%;text-align: left">包装</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="goods: ${goodsList}">
                                    <td style="text-align: left" th:text="${goods.goodsName}"></td>
                                    <td style="text-align: left" th:text="${goods.goodsTypeName}"></td>
                                    <td style="text-align: left" th:text="${goods.num}"></td>
                                    <td style="text-align: left" th:text="${goods.weight}"></td>
                                    <td style="text-align: left" th:text="${goods.volume}"></td>
                                    <td style="text-align: left" th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${goods.packId}" th:text="${dict.dictLabel}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--订单货品费用明细 end-->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseSix">物流跟踪</a>
                    </h4>
                </div>
                <div id="collapseSix" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">实际提货：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group" th:text="|${actDeliDate}|">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">实际到货：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group" th:text="|${actArriDate}|">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">回单时间：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group" th:text="|${receiptDate}|">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12" th:if="${pickUpPicList.size() > 0}">
                                <div class="form-group">
                                    <label class="col-sm-1">提货照片：</label>
                                    <div class="col-sm-11">
                                        <div class="media-container">
                                            <!-- 图片容器 -->
                                            <div class="picviewer" style="display: inline-block;">
                                                <span th:each="pic:${pickUpPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.jpg') or #strings.toLowerCase(pic).endsWith('.jpeg') or #strings.toLowerCase(pic).endsWith('.png') or #strings.toLowerCase(pic).endsWith('.gif') or #strings.toLowerCase(pic).endsWith('.bmp')}">
                                                    <img style="height:50px;margin-right:5px;cursor:pointer;" th:src="@{${pic}}"/>
                                                </span>
                                            </div>
                                            <!-- 视频容器 -->
                                            <div class="video-container" style="display: inline-block;">
                                                <span th:each="pic:${pickUpPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.mp4') or #strings.toLowerCase(pic).endsWith('.avi') or #strings.toLowerCase(pic).endsWith('.mov') or #strings.toLowerCase(pic).endsWith('.wmv') or #strings.toLowerCase(pic).endsWith('.flv') or #strings.toLowerCase(pic).endsWith('.webm')}">
                                                    <div class="video-thumbnail" th:data-video="@{${pic}}" style="height:50px;width:70px;margin-right:5px;cursor:pointer;border:1px solid #ddd;border-radius:4px;background:#f5f5f5;display:inline-flex;align-items:center;justify-content:center;position:relative;">
                                                        <i class="fa fa-play-circle" style="font-size:20px;color:#337ab7;"></i>
                                                        <span style="position:absolute;bottom:2px;right:2px;background:rgba(0,0,0,0.7);color:white;font-size:10px;padding:1px 3px;border-radius:2px;">视频</span>
                                                    </div>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12" th:if="${arrivalPicList.size() > 0}">
                                <div class="form-group">
                                    <label class="col-sm-1">到货照片：</label>
                                    <div class="col-sm-11">
                                        <div class="media-container">
                                            <!-- 图片容器 -->
                                            <div class="picviewer" style="display: inline-block;">
                                                <span th:each="pic:${arrivalPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.jpg') or #strings.toLowerCase(pic).endsWith('.jpeg') or #strings.toLowerCase(pic).endsWith('.png') or #strings.toLowerCase(pic).endsWith('.gif') or #strings.toLowerCase(pic).endsWith('.bmp')}">
                                                    <img style="height:50px;margin-right:5px;cursor:pointer;" th:src="@{${pic}}"/>
                                                </span>
                                            </div>
                                            <!-- 视频容器 -->
                                            <div class="video-container" style="display: inline-block;">
                                                <span th:each="pic:${arrivalPicList}" th:if="${#strings.toLowerCase(pic).endsWith('.mp4') or #strings.toLowerCase(pic).endsWith('.avi') or #strings.toLowerCase(pic).endsWith('.mov') or #strings.toLowerCase(pic).endsWith('.wmv') or #strings.toLowerCase(pic).endsWith('.flv') or #strings.toLowerCase(pic).endsWith('.webm')}">
                                                    <div class="video-thumbnail" th:data-video="@{${pic}}" style="height:50px;width:70px;margin-right:5px;cursor:pointer;border:1px solid #ddd;border-radius:4px;background:#f5f5f5;display:inline-flex;align-items:center;justify-content:center;position:relative;">
                                                        <i class="fa fa-play-circle" style="font-size:20px;color:#337ab7;"></i>
                                                        <span style="position:absolute;bottom:2px;right:2px;background:rgba(0,0,0,0.7);color:white;font-size:10px;padding:1px 3px;border-radius:2px;">视频</span>
                                                    </div>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 col-sm-12" th:if="${picList.size() > 0}">
                                <div class="form-group">
                                    <label class="col-sm-1">回单照片：</label>
                                    <div class="col-sm-11">
                                        <div class="media-container">
                                            <!-- 图片容器 -->
                                            <div class="picviewer" style="display: inline-block;">
                                                <span th:each="pic:${picList}" th:if="${#strings.toLowerCase(pic).endsWith('.jpg') or #strings.toLowerCase(pic).endsWith('.jpeg') or #strings.toLowerCase(pic).endsWith('.png') or #strings.toLowerCase(pic).endsWith('.gif') or #strings.toLowerCase(pic).endsWith('.bmp')}">
                                                    <img style="height:50px;margin-right:5px;cursor:pointer;" th:src="@{${pic}}"/>
                                                </span>
                                            </div>
                                            <!-- 视频容器 -->
                                            <div class="video-container" style="display: inline-block;">
                                                <span th:each="pic:${picList}" th:if="${#strings.toLowerCase(pic).endsWith('.mp4') or #strings.toLowerCase(pic).endsWith('.avi') or #strings.toLowerCase(pic).endsWith('.mov') or #strings.toLowerCase(pic).endsWith('.wmv') or #strings.toLowerCase(pic).endsWith('.flv') or #strings.toLowerCase(pic).endsWith('.webm')}">
                                                    <div class="video-thumbnail" th:data-video="@{${pic}}" style="height:50px;width:70px;margin-right:5px;cursor:pointer;border:1px solid #ddd;border-radius:4px;background:#f5f5f5;display:inline-flex;align-items:center;justify-content:center;position:relative;">
                                                        <i class="fa fa-play-circle" style="font-size:20px;color:#337ab7;"></i>
                                                        <span style="position:absolute;bottom:2px;right:2px;background:rgba(0,0,0,0.7);color:white;font-size:10px;padding:1px 3px;border-radius:2px;">视频</span>
                                                    </div>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapsesix">操作记录</a>
                    </h4>
                </div>
                <div id="collapseseven" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--操作记录 begin-->
                        <div class="over">
                            <div class="fl" style="color: #808080">发货单编号:[[${invoice.vbillno}]]</div>
                            <div class="fl" style="margin-left: 40px;color: #808080">创建人:[[${invoice.regUserName}]]</div>
                        </div>
                        <div class="mt20 recode">
                            <div class="row">
                                <div class="col-sm-6 col-lg-4" style="padding-right: 0;">
                                    <div class="timeline borgreen col-sm-6">
                                        <div class="timeline_point bggreen"></div>
                                        <div class="row">
                                            <div class="col-sm-12" style="padding: 0">
                                                <div class="timeline_text bordash">
                                                    <div class="fw">新建单据</div>
                                                    <div class="f12 fc80">[[${invoice.regUserName}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </div>
                                                <div class="" th:if="${#lists.size(receiveDetailList) > 0}">
                                                    <div class="tc bordash fw fc1ab" style="padding: 20px 0"></div>

                                                </div>
                                            </div>
                                           

                                        </div>
                                    </div>
                                    <div class="timeline borgreen col-sm-6" style="padding-right: 0;">
                                        <div class="timeline_point bggreen"></div>
                                        <div class="row">
                                            <div class="col-sm-10" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">订单确认</div>
                                                    <div class="f12 fc80">[[${invoice.confirmUserid}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(invoice.confirmDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    <!--                                                        <div class="f12 fc80">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                                </div>
                                            </div>
                                            <div class="col-sm-2" style="padding: 0">
                                                <div class="vercontent" th:if="${#lists.size(operationHistoryVOList) > 1}"></div>
                                            </div>
                                            <!-- <div class="col-sm-7" style="padding: 0">
                                                d <iv class="flowcontent" th:if="${#lists.size(operationHistoryVO.payDetailList) > 0}">
                                                     <div class="" style="padding-left: 5px">
                                                         <div class="tc bordashle fw fc1ab" style="padding: 20px 0"></div>

                                                     </div>
                                                 </div>
                                             </div>-->

                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-8" style="padding-left: 0;">
                                    <div style="padding: 0 0 20px 0" class="row"
                                         th:if="${#lists.size(operationHistoryVOList) > 0}"
                                         th:each="operationHistoryVO,stat:${operationHistoryVOList}"
                                         th:classappend="${stat.count >1?'vercontent2':'padbt20'}">
                                        <div class="timeline col-sm-4"
                                             th:classappend="${operationHistoryVO.vbillstatus == '2'
                                                || operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                                            <div class="timeline_point"
                                                 th:classappend="${operationHistoryVO.vbillstatus == '2'
                                                    || operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">提货</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.pickUpUserName}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.pickUpDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0" th:if="${#lists.size(operationHistoryVO.carLocusList) > 0}">
                                                    <div class="flowcontent">
                                                        <div class="" style="padding-left: 5px">
                                                            <div class="tc bordashle fw fc1ab" style="padding: 20px 0">行程跟踪</div>
                                                            <div class="dashed bordashle"
                                                                 th:each="carLocus:${operationHistoryVO.carLocusList}">
                                                                <div class="f12 fc80 tc">[[${#dates.format(carLocus.trackingTime, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                                <div class="f12 tc">[[${carLocus.proName+carLocus.cityName+carLocus.areaName+carLocus.detailAddr}]]</div>
                                                                <div class="round roul bg00a"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-4"
                                             th:classappend="${operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                                            <div class="timeline_point2"
                                                 th:classappend="${operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">到货</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.arrivalsUserName}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.arrivalsDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-4"
                                             th:classappend="${operationHistoryVO.receiptMan != null ?'borgreen':'bordark'}">
                                            <div class="timeline_point2"
                                                 th:classappend="${operationHistoryVO.receiptMan != null ?'bggreen':'bgdark'}"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">回单</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.receiptMan}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <!--                                        <div class="timeline col-sm-2 bordark">-->
                                        <!--                                            <div class="timeline_point2 bgdark"></div>-->
                                        <!--                                            <div class="row">-->
                                        <!--                                                <div class="col-sm-12" style="padding: 0">-->
                                        <!--                                                    <div class="timeline_text">-->
                                        <!--                                                        <div class="fw">签收</div>-->
                                        <!--                                                        <div class="f12 fc80">张三三</div>-->
                                        <!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
                                        <!--                                                    </div>-->
                                        <!--                                                </div>-->

                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                    </div>
                                    <div class="row vercontent2" th:if="${#lists.size(operationHistoryVOList) == 0}">
                                        <div class="timeline col-sm-3 bordark">
                                            <div class="timeline_point bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">调度一</div>
                                                        <!--                                                        <div class="f12 fc80">张三三</div>-->
                                                        <!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
                                                        <!--                                                        <div class="f12 fc80 eclipse">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0">
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-3 bordark">
                                            <div class="timeline_point bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">提货</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0">
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-3 bordark">
                                            <div class="timeline_point2 bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">到货</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-3 bordark">
                                            <div class="timeline_point2 bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">回单</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <!--                                        <div class="timeline col-sm-2 bordark">-->
                                        <!--                                            <div class="timeline_point2 bgdark"></div>-->
                                        <!--                                            <div class="row">-->
                                        <!--                                                <div class="col-sm-12" style="padding: 0">-->
                                        <!--                                                    <div class="timeline_text">-->
                                        <!--                                                        <div class="fw">签收</div>-->
                                        <!--                                                        <div class="f12 fc80">张三三</div>-->
                                        <!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
                                        <!--                                                    </div>-->
                                        <!--                                                </div>-->

                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--操作记录 end-->
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<!-- 视频模态框 -->
<div id="videoModal" class="video-modal">
    <span class="video-modal-close">&times;</span>
    <div class="video-modal-content">
        <video id="modalVideo" controls>
            您的浏览器不支持视频播放
        </video>
    </div>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script>
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseseven').collapse('show');

        // 处理视频缩略图点击事件
        $(document).on('click', '.video-thumbnail', function() {
            var videoSrc = $(this).data('video');
            var modal = $('#videoModal');
            var modalVideo = $('#modalVideo');

            modalVideo.attr('src', videoSrc);
            modal.show();

            // 自动播放视频
            modalVideo[0].play();
        });

        // 关闭视频模态框
        $('.video-modal-close, .video-modal').on('click', function(e) {
            if (e.target === this) {
                var modal = $('#videoModal');
                var modalVideo = $('#modalVideo');

                modalVideo[0].pause();
                modalVideo.attr('src', '');
                modal.hide();
            }
        });

        // 阻止视频区域点击事件冒泡
        $('.video-modal-content').on('click', function(e) {
            e.stopPropagation();
        });

        // ESC键关闭模态框
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // ESC键
                var modal = $('#videoModal');
                if (modal.is(':visible')) {
                    var modalVideo = $('#modalVideo');
                    modalVideo[0].pause();
                    modalVideo.attr('src', '');
                    modal.hide();
                }
            }
        });
    });


</script>
</body>

</html>